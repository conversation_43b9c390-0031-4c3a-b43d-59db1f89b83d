use crate::{
    mailers::admin_auth::Ad<PERSON><PERSON>uthMail<PERSON>,
    models::{
        _entities::admins,
        admins::LoginParams,
    },
    views::admin_auth::{CurrentResponse, LoginResponse},
};
use axum::debug_handler;
use loco_rs::prelude::*;
use regex::Regex;
use serde::{Deserialize, Serialize};
use std::sync::OnceLock;

pub static EMAIL_DOMAIN_RE: OnceLock<Regex> = OnceLock::new();

fn get_allow_email_domain_re() -> &'static Regex {
    EMAIL_DOMAIN_RE.get_or_init(|| {
        Regex::new(r"@example\.com$|@gmail\.com$").expect("Failed to compile regex")
    })
}

#[derive(Debug, Deserialize, Serialize)]
pub struct ForgotParams {
    pub email: String,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct ResetParams {
    pub token: String,
    pub password: String,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct MagicLinkParams {
    pub email: String,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct ChangePasswordParams {
    pub current_password: String,
    pub new_password: String,
    pub confirm_password: String,
}

// 注册功能已移除 - 管理员账户只能通过数据库初始化创建

/// Verify register admin. if the admin not verified his email, he can't login to
/// the system.
#[debug_handler]
async fn verify(State(ctx): State<AppContext>, Path(token): Path<String>) -> Result<Response> {
    let admin = admins::Model::find_by_verification_token(&ctx.db, &token).await?;

    if admin.email_verified_at.is_some() {
        tracing::info!(pid = admin.pid.to_string(), "admin already verified");
    } else {
        let active_model = admin.into_active_model();
        let admin = active_model.verified(&ctx.db).await?;
        tracing::info!(pid = admin.pid.to_string(), "admin verified");
    }

    format::json(())
}

/// In case the admin forgot his password  this endpoints generate a forgot token
/// and send email to the admin. In case the email not found in our DB, we are
/// returning a valid request for for security reasons (not exposing admins DB
/// list).
#[debug_handler]
async fn forgot(
    State(ctx): State<AppContext>,
    Json(params): Json<ForgotParams>,
) -> Result<Response> {
    let Ok(admin) = admins::Model::find_by_email(&ctx.db, &params.email).await else {
        // we don't want to expose our admins email. if the email is invalid we still
        // returning success to the caller
        return format::json(());
    };

    let admin = admin
        .into_active_model()
        .set_forgot_password_sent(&ctx.db)
        .await?;

    AdminAuthMailer::forgot_password(&ctx, &admin).await?;

    format::json(())
}

/// reset admin password by the given parameters
#[debug_handler]
async fn reset(State(ctx): State<AppContext>, Json(params): Json<ResetParams>) -> Result<Response> {
    let Ok(admin) = admins::Model::find_by_reset_token(&ctx.db, &params.token).await else {
        // we don't want to expose our admins email. if the email is invalid we still
        // returning success to the caller
        tracing::info!("reset token not found");

        return format::json(());
    };
    admin
        .into_active_model()
        .reset_password(&ctx.db, &params.password)
        .await?;

    format::json(())
}

/// Creates a admin login and returns a token
#[debug_handler]
async fn login(State(ctx): State<AppContext>, Json(params): Json<LoginParams>) -> Result<Response> {
    let admin = admins::Model::find_by_email(&ctx.db, &params.email).await?;

    let valid = admin.verify_password(&params.password);

    if !valid {
        return unauthorized("unauthorized!");
    }

    // let admin = admin.into_active_model();

    let jwt_secret = ctx.config.get_jwt_config()?;

    let token = admin
        .generate_jwt(&jwt_secret.secret, jwt_secret.expiration)
        .or_else(|_| unauthorized("unauthorized!"))?;

    format::json(LoginResponse::new(&admin, &token))
}

#[debug_handler]
async fn current(auth: auth::JWT, State(ctx): State<AppContext>) -> Result<Response> {
    let admin = admins::Model::find_by_pid(&ctx.db, &auth.claims.pid).await?;
    format::json(CurrentResponse::new(&admin))
}

/// Magic link authentication provides a secure and passwordless way to log in to the application.
///
/// # Flow
/// 1. **Request a Magic Link**:
///    A registered admin sends a POST request to `/magic-link` with their email.
///    If the email exists, a short-lived, one-time-use token is generated and sent to the admin's email.
///    For security and to avoid exposing whether an email exists, the response always returns 200, even if the email is invalid.
///
/// 2. **Click the Magic Link**:
///    The admin clicks the link (/magic-link/{token}), which validates the token and its expiration.
///    If valid, the server generates a JWT and responds with a [`LoginResponse`].
///    If invalid or expired, an unauthorized response is returned.
///
/// This flow enhances security by avoiding traditional passwords and providing a seamless login experience.
async fn magic_link(
    State(ctx): State<AppContext>,
    Json(params): Json<MagicLinkParams>,
) -> Result<Response> {
    let email_regex = get_allow_email_domain_re();
    if !email_regex.is_match(&params.email) {
        tracing::debug!(
            email = params.email,
            "The provided email is invalid or does not match the allowed domains"
        );
        return bad_request("invalid request");
    }

    let Ok(admin) = admins::Model::find_by_email(&ctx.db, &params.email).await else {
        // we don't want to expose our admins email. if the email is invalid we still
        // returning success to the caller
        tracing::debug!(email = params.email, "admin not found by email");
        return format::empty_json();
    };

    let admin = admin.into_active_model().create_magic_link(&ctx.db).await?;
    AdminAuthMailer::send_magic_link(&ctx, &admin).await?;

    format::empty_json()
}

/// Verifies a magic link token and authenticates the admin.
async fn magic_link_verify(
    Path(token): Path<String>,
    State(ctx): State<AppContext>,
) -> Result<Response> {
    let Ok(admin) = admins::Model::find_by_magic_token(&ctx.db, &token).await else {
        // we don't want to expose our admins email. if the email is invalid we still
        // returning success to the caller
        return unauthorized("unauthorized!");
    };

    let admin = admin.into_active_model().clear_magic_link(&ctx.db).await?;



    let jwt_secret = ctx.config.get_jwt_config()?;

    let token = admin
        .generate_jwt(&jwt_secret.secret, jwt_secret.expiration)
        .or_else(|_| unauthorized("unauthorized!"))?;

    format::json(LoginResponse::new(&admin, &token))
}

/// Change admin password
#[debug_handler]
async fn change_password(
    auth: auth::JWT,
    State(ctx): State<AppContext>,
    Json(params): Json<ChangePasswordParams>,
) -> Result<Response> {
    // 验证新密码和确认密码是否一致
    if params.new_password != params.confirm_password {
        return bad_request("new password and confirm password do not match");
    }

    // 获取当前管理员
    let admin = admins::Model::find_by_pid(&ctx.db, &auth.claims.pid).await?;

    // 验证当前密码
    if !admin.verify_password(&params.current_password) {
        return bad_request("current password is incorrect");
    }

    // 更新密码
    admin
        .into_active_model()
        .change_password(&ctx.db, &params.new_password)
        .await?;

    format::json(())
}

pub fn routes() -> Routes {
    Routes::new()
        .prefix("/api/admin/auth")
        .add("/verify/{token}", get(verify))
        .add("/login", post(login))
        .add("/forgot", post(forgot))
        .add("/reset", post(reset))
        .add("/current", get(current))
        .add("/change-password", post(change_password))
        .add("/magic-link", post(magic_link))
        .add("/magic-link/{token}", get(magic_link_verify))
}
