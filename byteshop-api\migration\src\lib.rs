#![allow(elided_lifetimes_in_paths)]
#![allow(clippy::wildcard_imports)]
pub use sea_orm_migration::prelude::*;
mod m20220101_000001_users;
mod m20240410_000002_create_products;
mod m20240417_000003_create_admins;

pub struct Migrator;

#[async_trait::async_trait]
impl MigratorTrait for Migrator {
    fn migrations() -> Vec<Box<dyn MigrationTrait>> {
        vec![
            Box::new(m20220101_000001_users::Migration),
            Box::new(m20240410_000002_create_products::Migration),
            Box::new(m20240417_000003_create_admins::Migration),
            // inject-above (do not remove this comment)
        ]
    }
}
