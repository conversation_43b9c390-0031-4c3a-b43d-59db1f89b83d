<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { useToast } from 'primevue/usetoast';
import { useConfirm } from 'primevue/useconfirm';

const router = useRouter();
const authStore = useAuthStore();
const toast = useToast();
const confirm = useConfirm();

const sidebarVisible = ref(true);
const user = computed(() => authStore.user);
const menu = ref();

// 在组件挂载时获取用户信息
onMounted(async () => {
  if (authStore.token && !authStore.user) {
    try {
      await authStore.fetchCurrentUser();
    } catch (error) {
      console.error('Failed to fetch current user:', error);
    }
  }
});

const toggleSidebar = () => {
  sidebarVisible.value = !sidebarVisible.value;
};

const handleLogout = () => {
  confirm.require({
    message: '确定要退出登录吗？',
    header: '退出确认',
    icon: 'pi pi-exclamation-triangle',
    rejectClass: 'p-button-secondary p-button-outlined',
    rejectLabel: '取消',
    acceptLabel: '确定退出',
    accept: async () => {
      try {
        await authStore.logout();
        // 显示成功消息
        toast.add({
          severity: 'success',
          summary: '退出成功',
          detail: '您已成功退出登录',
          life: 3000
        });
        // 跳转到登录页面
        router.push('/login');
      } catch (error) {
        // 显示错误消息
        toast.add({
          severity: 'error',
          summary: '退出失败',
          detail: error.message || '退出登录时发生错误',
          life: 5000
        });
        // 即使出错也跳转到登录页面
        router.push('/login');
      }
    }
  });
};

const menuItems = [
  {
    label: '仪表盘',
    icon: 'pi pi-home',
    to: '/dashboard'
  },
  {
    label: '商品管理',
    icon: 'pi pi-shopping-cart',
    items: [
      {
        label: '商品列表',
        icon: 'pi pi-list',
        to: '/products'
      },
      {
        label: '添加商品',
        icon: 'pi pi-plus',
        to: '/products/create'
      }
    ]
  },
  {
    label: '订单管理',
    icon: 'pi pi-inbox',
    to: '/orders'
  },
  {
    label: '用户管理',
    icon: 'pi pi-users',
    to: '/users'
  },
  {
    label: '管理员管理',
    icon: 'pi pi-shield',
    items: [
      {
        label: '注册管理员',
        icon: 'pi pi-user-plus',
        command: () => router.push('/register-admin')
      }
    ]
  },
  {
    label: '系统设置',
    icon: 'pi pi-cog',
    to: '/settings'
  }
];
</script>

<template>
  <Toast position="top-right" />
  <ConfirmDialog />
  <div class="layout-wrapper">
    <!-- 顶部导航栏 -->
    <div class="layout-topbar">
      <div class="topbar-start">
        <button class="menu-button p-link" @click="toggleSidebar">
          <i class="pi pi-bars"></i>
        </button>
        <div class="app-logo">
          <router-link to="/dashboard">
            <h1>ByteShop Admin</h1>
          </router-link>
        </div>
      </div>
      
      <div class="topbar-end">
        <Menu ref="menu" :model="[
          { label: '个人资料', icon: 'pi pi-user', command: () => router.push('/profile') },
          { label: '修改密码', icon: 'pi pi-lock', command: () => router.push('/change-password') },
          { separator: true },
          { label: '退出登录', icon: 'pi pi-sign-out', command: handleLogout }
        ]" popup />

        <Button
          :label="user ? user.name.charAt(0) : 'A'"
          class="user-avatar-button"
          rounded
          @click="(event) => menu.toggle(event)"
        />
      </div>
    </div>
    
    <!-- 侧边栏 -->
    <div class="layout-sidebar" :class="{ 'active': sidebarVisible }">
      <PanelMenu :model="menuItems" class="sidebar-menu" />
    </div>
    
    <!-- 主内容区 -->
    <div class="layout-main" :class="{ 'sidebar-active': sidebarVisible }">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped>
.layout-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  background-color: var(--surface-ground);
}

.layout-topbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: var(--surface-card);
  color: var(--text-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.topbar-start {
  display: flex;
  align-items: center;
}

.topbar-end {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  min-height: 40px;
}

.menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.menu-button:hover {
  background-color: var(--surface-hover);
}

.menu-button i {
  font-size: 1.25rem;
  color: var(--text-color);
}

.app-logo {
  display: flex;
  align-items: center;
}

.app-logo h1 {
  font-size: 1.25rem;
  margin: 0;
  color: var(--primary-color);
}

.app-logo a {
  text-decoration: none;
}

.user-avatar-button {
  width: 40px;
  height: 40px;
  min-width: 40px;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: #ffffff;
  margin-left: 1rem;
  transition: transform 0.2s;
  font-weight: bold;
}

.user-avatar-button:hover {
  transform: scale(1.1);
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.user-avatar-button:focus {
  box-shadow: 0 0 0 2px var(--primary-color-text);
}

.layout-sidebar {
  position: fixed;
  top: 60px;
  left: 0;
  width: 250px;
  height: calc(100% - 60px);
  background-color: var(--surface-card);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 998;
  overflow-y: auto;
  transition: transform 0.2s;
}

.layout-sidebar:not(.active) {
  transform: translateX(-100%);
}

.sidebar-menu {
  padding: 1rem;
}

.sidebar-menu :deep(.p-panelmenu) {
  border: none;
}

.sidebar-menu :deep(.p-panelmenu-header-link) {
  padding: 0.75rem 1rem;
}

.sidebar-menu :deep(.p-menuitem-icon) {
  margin-right: 0.5rem;
}

.layout-main {
  margin-top: 60px;
  margin-left: 0;
  transition: margin-left 0.2s;
  flex: 1;
}

.layout-main.sidebar-active {
  margin-left: 250px;
}

@media (max-width: 991px) {
  .layout-main.sidebar-active {
    margin-left: 0;
  }
  
  .layout-sidebar {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  }
}
</style>
