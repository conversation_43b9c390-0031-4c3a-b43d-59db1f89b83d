{"name": "byteshop-admin", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@primeuix/themes": "^1.0.3", "pinia": "^3.0.1", "primevue": "^4.3.3", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@primevue/auto-import-resolver": "^4.3.3", "@vitejs/plugin-vue": "^5.2.3", "unplugin-vue-components": "^28.5.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}