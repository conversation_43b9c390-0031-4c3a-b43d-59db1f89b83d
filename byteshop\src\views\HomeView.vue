<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const products = ref([]);
const loading = ref(true);

// 模拟产品数据
const mockProducts = [
  {
    id: 1,
    name: '软件开发工具套件',
    description: '专业的软件开发工具套件，包含代码编辑器、调试器等多种工具。',
    price: 299.99,
    image: 'https://via.placeholder.com/300x200?text=软件开发工具',
  },
  {
    id: 2,
    name: '数据分析软件',
    description: '强大的数据分析软件，支持多种数据格式和分析方法。',
    price: 199.99,
    image: 'https://via.placeholder.com/300x200?text=数据分析软件',
  },
  {
    id: 3,
    name: '图形设计软件',
    description: '专业的图形设计软件，支持多种设计需求。',
    price: 249.99,
    image: 'https://via.placeholder.com/300x200?text=图形设计软件',
  },
  {
    id: 4,
    name: '办公软件套件',
    description: '包含文字处理、电子表格、演示文稿等办公软件。',
    price: 149.99,
    image: 'https://via.placeholder.com/300x200?text=办公软件套件',
  },
];

onMounted(() => {
  // 模拟加载数据
  setTimeout(() => {
    products.value = mockProducts;
    loading.value = false;
  }, 1000);
});

const navigateToProduct = (productId) => {
  router.push(`/product/${productId}`);
};
</script>

<template>
  <div class="home-container">
    <!-- 顶部横幅 -->
    <section class="hero-section">
      <div class="container">
        <div class="hero-content">
          <h1>欢迎来到 ByteShop</h1>
          <p>您的专业软件许可购买平台</p>
          <Button label="浏览商品" icon="pi pi-search" class="p-button-lg" />
        </div>
      </div>
    </section>

    <!-- 商品列表 -->
    <section class="products-section">
      <div class="container">
        <h2>热门软件</h2>

        <div v-if="loading" class="loading-container">
          <i class="pi pi-spin pi-spinner" style="font-size: 2rem"></i>
          <p>正在加载商品...</p>
        </div>

        <div v-else class="products-grid">
          <Card v-for="product in products" :key="product.id" class="product-card">
            <template #header>
              <img :src="product.image" :alt="product.name" class="product-image" />
            </template>
            <template #title>
              {{ product.name }}
            </template>
            <template #subtitle>
              ¥{{ product.price.toFixed(2) }}
            </template>
            <template #content>
              <p>{{ product.description }}</p>
            </template>
            <template #footer>
              <div class="card-footer">
                <Button label="查看详情" icon="pi pi-info-circle" @click="navigateToProduct(product.id)" />
                <Button label="立即购买" icon="pi pi-shopping-cart" class="p-button-success" />
              </div>
            </template>
          </Card>
        </div>
      </div>
    </section>

    <!-- 特点介绍 -->
    <section class="features-section">
      <div class="container">
        <h2>我们的特点</h2>

        <div class="features-grid">
          <div class="feature-item">
            <i class="pi pi-shield" style="font-size: 2rem"></i>
            <h3>安全可靠</h3>
            <p>所有许可证都经过严格验证，确保您的购买安全可靠。</p>
          </div>

          <div class="feature-item">
            <i class="pi pi-bolt" style="font-size: 2rem"></i>
            <h3>快速交付</h3>
            <p>购买后立即获得许可证，无需等待。</p>
          </div>

          <div class="feature-item">
            <i class="pi pi-users" style="font-size: 2rem"></i>
            <h3>专业支持</h3>
            <p>我们的技术支持团队随时为您解决问题。</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped>
.home-container {
  width: 100%;
}

/* 顶部横幅 */
.hero-section {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-700) 100%);
  color: white;
  padding: 4rem 0;
  text-align: center;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-content h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

/* 商品列表 */
.products-section {
  padding: 3rem 0;
}

.products-section h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--text-color);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
}

.product-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
}

/* 特点介绍 */
.features-section {
  background-color: var(--surface-section);
  padding: 3rem 0;
}

.features-section h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--text-color);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.feature-item {
  text-align: center;
  padding: 1.5rem;
  background-color: var(--surface-card);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.feature-item i {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.feature-item h3 {
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.feature-item p {
  color: var(--text-color-secondary);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 2rem;
  }

  .hero-content p {
    font-size: 1rem;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 1rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}
</style>
