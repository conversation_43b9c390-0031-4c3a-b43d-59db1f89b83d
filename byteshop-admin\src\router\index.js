import { createRouter, createWebHistory } from 'vue-router';
import { useAuthStore } from '@/stores/auth';

// 认证相关页面
import LoginView from '@/views/auth/LoginView.vue';
import ForgotPasswordView from '@/views/auth/ForgotPasswordView.vue';
import ResetPasswordView from '@/views/auth/ResetPasswordView.vue';
import MagicLinkSentView from '@/views/auth/MagicLinkSentView.vue';
import MagicLinkLoginView from '@/views/auth/MagicLinkLoginView.vue';
import VerifyEmailView from '@/views/auth/VerifyEmailView.vue';

// 仪表盘页面
const DashboardView = () => import('@/views/DashboardView.vue');

// 产品管理页面
const ProductsView = () => import('@/views/products/ProductsView.vue');
const ProductDetailView = () => import('@/views/products/ProductDetailView.vue');
const ProductCreateView = () => import('@/views/products/ProductCreateView.vue');
const ProductEditView = () => import('@/views/products/ProductEditView.vue');

// 管理员页面
const ProfileView = () => import('@/views/admin/ProfileView.vue');

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // 公开路由
    {
      path: '/login',
      name: 'login',
      component: LoginView,
      meta: { requiresAuth: false }
    },
    {
      path: '/forgot-password',
      name: 'forgot-password',
      component: ForgotPasswordView,
      meta: { requiresAuth: false }
    },
    {
      path: '/reset-password/:token',
      name: 'reset-password',
      component: ResetPasswordView,
      meta: { requiresAuth: false }
    },
    {
      path: '/magic-link-sent/:email',
      name: 'magic-link-sent',
      component: MagicLinkSentView,
      meta: { requiresAuth: false }
    },
    {
      path: '/magic-link/:token',
      name: 'magic-link-login',
      component: MagicLinkLoginView,
      meta: { requiresAuth: false }
    },
    {
      path: '/verify-email/:token',
      name: 'verify-email',
      component: VerifyEmailView,
      meta: { requiresAuth: false }
    },

    // 需要认证的路由
    {
      path: '/',
      redirect: '/dashboard',
      meta: { requiresAuth: true }
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: DashboardView,
      meta: { requiresAuth: true }
    },

    // 产品管理路由
    {
      path: '/products',
      name: 'products',
      component: ProductsView,
      meta: { requiresAuth: true }
    },
    {
      path: '/products/create',
      name: 'product-create',
      component: ProductCreateView,
      meta: { requiresAuth: true }
    },
    {
      path: '/products/:id',
      name: 'product-detail',
      component: ProductDetailView,
      meta: { requiresAuth: true }
    },
    {
      path: '/products/:id/edit',
      name: 'product-edit',
      component: ProductEditView,
      meta: { requiresAuth: true }
    },

    // 管理员页面
    {
      path: '/profile',
      name: 'profile',
      component: ProfileView,
      meta: { requiresAuth: true }
    },

    // 404 页面
    {
      path: '/:pathMatch(.*)*',
      redirect: '/dashboard'
    }
  ]
});

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore();

  // 如果路由需要认证
  if (to.meta.requiresAuth) {
    // 检查是否已登录
    if (!authStore.token) {
      // 未登录，重定向到登录页
      return next({ name: 'login' });
    }

    // 如果有token但没有用户信息，尝试获取用户信息
    if (authStore.token && !authStore.user) {
      try {
        await authStore.fetchCurrentUser();
      } catch (error) {
        // 获取用户信息失败，清除token并重定向到登录页
        authStore.clearAuth();
        return next({ name: 'login' });
      }
    }

    // 已登录，允许访问
    return next();
  }

  // 如果路由不需要认证
  if (!to.meta.requiresAuth && authStore.isAuthenticated) {
    // 已登录，访问不需要认证的页面，重定向到仪表盘
    if (to.name === 'login' || to.name === 'forgot-password' || to.name === 'reset-password') {
      return next({ name: 'dashboard' });
    }
  }

  // 其他情况，允许访问
  next();
});

export default router;
