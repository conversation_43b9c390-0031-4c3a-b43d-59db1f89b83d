use serde::{Deserialize, Serialize};

use crate::models::_entities::admins;

#[derive(Debug, Deserialize, Serialize)]
pub struct LoginResponse {
    pub token: String,
    pub pid: String,
    pub name: String,
    pub is_verified: bool,
}

impl LoginResponse {
    #[must_use]
    pub fn new(admin: &admins::Model, token: &String) -> Self {
        Self {
            token: token.to_string(),
            pid: admin.pid.to_string(),
            name: admin.name.clone(),
            is_verified: admin.email_verified_at.is_some(),
        }
    }
}

#[derive(Debug, Deserialize, Serialize)]
pub struct CurrentResponse {
    pub pid: String,
    pub name: String,
    pub email: String,
}

impl CurrentResponse {
    #[must_use]
    pub fn new(admin: &admins::Model) -> Self {
        Self {
            pid: admin.pid.to_string(),
            name: admin.name.clone(),
            email: admin.email.clone(),
        }
    }
}
