<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import FormInput from '@/components/ui/FormInput.vue';

const router = useRouter();
const authStore = useAuthStore();

const form = ref({
  name: '',
  email: '',
});

const errors = ref({});
const successMessage = ref('');
const loading = ref(false);

onMounted(async () => {
  // 如果用户未登录，跳转到登录页
  if (!authStore.isAuthenticated) {
    router.push('/login');
    return;
  }

  // 获取用户信息
  try {
    await authStore.fetchCurrentUser();
    form.value.name = authStore.user.name;
    form.value.email = authStore.user.email;
  } catch (error) {
    errors.value.general = '获取用户信息失败';
  }
});

const handleLogout = async () => {
  await authStore.logout();
  router.push('/login');
};
</script>

<template>
  <div class="profile-container">
    <div class="card">
      <h1>个人中心</h1>
      
      <div v-if="errors.general" class="error-message">
        {{ errors.general }}
      </div>
      
      <div v-if="successMessage" class="success-message">
        {{ successMessage }}
      </div>
      
      <div class="user-info">
        <div class="avatar">
          {{ form.name ? form.name.charAt(0).toUpperCase() : 'U' }}
        </div>
        
        <div class="info">
          <h2>{{ form.name }}</h2>
          <p>{{ form.email }}</p>
        </div>
      </div>
      
      <div class="divider"></div>
      
      <div class="actions">
        <button class="btn-secondary" @click="handleLogout">
          退出登录
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.profile-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 2rem;
}

.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  width: 100%;
  max-width: 600px;
}

h1 {
  text-align: center;
  margin-bottom: 2rem;
  color: #333;
}

.success-message {
  background-color: #e8f5e9;
  color: #2e7d32;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  text-align: center;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  text-align: center;
}

.user-info {
  display: flex;
  align-items: center;
  margin: 2rem 0;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #2196f3;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 2rem;
  font-weight: bold;
  margin-right: 1.5rem;
}

.info h2 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.info p {
  margin: 0;
  color: #666;
}

.divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 2rem 0;
}

.actions {
  display: flex;
  justify-content: center;
}

.btn-secondary {
  background-color: white;
  color: #f44336;
  border: 1px solid #f44336;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-secondary:hover {
  background-color: #ffebee;
}
</style>
