<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useToast } from 'primevue/usetoast';
import api from '@/services/api';

const route = useRoute();
const router = useRouter();
const toast = useToast();

const product = ref(null);
const loading = ref(true);
const error = ref(null);

onMounted(async () => {
  const productId = route.params.id;
  await fetchProduct(productId);
});

const fetchProduct = async (id) => {
  loading.value = true;
  try {
    const response = await api.products.getProduct(id);
    product.value = response;
  } catch (err) {
    error.value = err.message || '获取商品详情失败';
    toast.add({
      severity: 'error',
      summary: '获取失败',
      detail: error.value,
      life: 5000
    });
  } finally {
    loading.value = false;
  }
};

const handleEdit = () => {
  router.push(`/products/${product.value.id}/edit`);
};

const handleDelete = async () => {
  try {
    await api.products.deleteProduct(product.value.id);
    toast.add({
      severity: 'success',
      summary: '删除成功',
      detail: '商品已成功删除',
      life: 3000
    });
    router.push('/products');
  } catch (err) {
    toast.add({
      severity: 'error',
      summary: '删除失败',
      detail: err.message || '删除商品失败',
      life: 5000
    });
  }
};

const formatPrice = (price) => {
  return `¥${price.toFixed(2)}`;
};
</script>

<template>
  <div class="product-detail-container">
    <div class="card">
      <div class="header">
        <Button 
          icon="pi pi-arrow-left" 
          text 
          @click="router.push('/products')" 
          class="back-button"
        />
        <h1>商品详情</h1>
        <div class="action-buttons">
          <Button 
            label="编辑" 
            icon="pi pi-pencil" 
            severity="success" 
            outlined
            @click="handleEdit" 
            class="mr-2"
          />
          <Button 
            label="删除" 
            icon="pi pi-trash" 
            severity="danger" 
            outlined
            @click="handleDelete" 
          />
        </div>
      </div>
      
      <div v-if="loading" class="loading-container">
        <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="4" />
        <p>加载中...</p>
      </div>
      
      <div v-else-if="error" class="error-message">
        <Message severity="error">{{ error }}</Message>
      </div>
      
      <div v-else-if="product" class="product-content">
        <div class="grid">
          <div class="col-12 md:col-4">
            <div class="product-image">
              <img 
                :src="product.image_url || 'https://via.placeholder.com/300x300?text=No+Image'" 
                :alt="product.name"
                class="w-full"
              />
            </div>
          </div>
          
          <div class="col-12 md:col-8">
            <div class="product-info">
              <h2>{{ product.name }}</h2>
              
              <div class="info-row">
                <div class="label">价格:</div>
                <div class="value price">{{ formatPrice(product.price) }}</div>
              </div>
              
              <div class="info-row">
                <div class="label">库存:</div>
                <div class="value">{{ product.stock }}</div>
              </div>
              
              <div class="info-row">
                <div class="label">商品ID:</div>
                <div class="value">{{ product.id }}</div>
              </div>
              
              <div class="info-row">
                <div class="label">创建时间:</div>
                <div class="value">{{ new Date(product.created_at).toLocaleString() }}</div>
              </div>
              
              <div class="info-row">
                <div class="label">更新时间:</div>
                <div class="value">{{ new Date(product.updated_at).toLocaleString() }}</div>
              </div>
              
              <Divider />
              
              <div class="description">
                <h3>商品描述</h3>
                <p>{{ product.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.product-detail-container {
  padding: 2rem;
}

.card {
  background: var(--surface-card);
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
}

.back-button {
  margin-right: 1rem;
}

h1 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--text-color);
  flex-grow: 1;
}

.action-buttons {
  display: flex;
}

.mr-2 {
  margin-right: 0.5rem;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
}

.loading-container p {
  margin-top: 1rem;
  color: var(--text-color-secondary);
}

.product-image img {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.product-info {
  padding: 0 1rem;
}

h2 {
  font-size: 1.75rem;
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: var(--text-color);
}

.info-row {
  display: flex;
  margin-bottom: 1rem;
}

.label {
  width: 100px;
  font-weight: 600;
  color: var(--text-color-secondary);
}

.value {
  flex-grow: 1;
}

.price {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-color);
}

.description h3 {
  font-size: 1.25rem;
  margin-bottom: 1rem;
  color: var(--text-color);
}

.description p {
  line-height: 1.6;
  color: var(--text-color-secondary);
  white-space: pre-line;
}

@media (max-width: 768px) {
  .product-detail-container {
    padding: 1rem;
  }
  
  .header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .back-button {
    margin-bottom: 1rem;
  }
  
  .action-buttons {
    margin-top: 1rem;
    width: 100%;
  }
  
  .action-buttons .p-button {
    flex: 1;
  }
  
  .product-info {
    padding: 0;
    margin-top: 1.5rem;
  }
}
</style>
