use axum::{extract::Path, Json};
use loco_rs::prelude::*;
use sea_orm::{ActiveModelTrait, ColumnTrait, EntityTrait, QueryFilter, Set};
use serde_json::json;

use crate::models::product::{
    self, ActiveModel, CreateProduct, Entity as Product, Model as ProductModel,
    ProductDetailResponse, ProductListResponse, UpdateProduct,
};

async fn model_to_list_response(model: &ProductModel) -> ProductListResponse {
    let image_urls: Vec<String> =
        serde_json::from_value(model.image_urls.clone()).unwrap_or_default();

    ProductListResponse {
        id: model.id,
        name: model.name.clone(),
        description: model.description.clone(),
        price: model.price,
        image_urls,
        is_active: model.is_active,
    }
}

async fn model_to_detail_response(model: &ProductModel) -> ProductDetailResponse {
    let image_urls: Vec<String> =
        serde_json::from_value(model.image_urls.clone()).unwrap_or_default();

    ProductDetailResponse {
        id: model.id,
        name: model.name.clone(),
        description: model.description.clone(),
        price: model.price,
        image_urls,
        details: model.details.clone(),
        is_active: model.is_active,
        created_at: model.created_at.to_string(),
        updated_at: model.updated_at.to_string(),
    }
}

/// 获取商品列表
pub async fn list(State(ctx): State<AppContext>) -> Result<Json<Vec<ProductListResponse>>> {
    let products = Product::find()
        .filter(product::Column::IsActive.eq(true))
        .all(&ctx.db)
        .await?;

    let mut response = Vec::new();
    for product in products {
        response.push(model_to_list_response(&product).await);
    }

    Ok(Json(response))
}

/// 获取商品详情
pub async fn get(
    Path(id): Path<i32>,
    State(ctx): State<AppContext>,
) -> Result<Json<ProductDetailResponse>> {
    let product = Product::find_by_id(id).one(&ctx.db).await?;

    if let Some(product) = product {
        Ok(Json(model_to_detail_response(&product).await))
    } else {
        Err(Error::string(format!("商品不存在: {}", id).as_str()))
    }
}

/// 创建商品（管理员）
pub async fn create(
    State(ctx): State<AppContext>,
    Json(payload): Json<CreateProduct>,
) -> Result<Json<ProductDetailResponse>> {
    // 将图片URL数组转换为JSON
    let image_urls = serde_json::to_value(payload.image_urls)?;

    let product = ActiveModel {
        name: Set(payload.name),
        description: Set(payload.description),
        price: Set(payload.price),
        image_urls: Set(image_urls),
        details: Set(payload.details),
        is_active: Set(payload.is_active.unwrap_or(true)),
        ..Default::default()
    };

    let product = product.insert(&ctx.db).await?;
    Ok(Json(model_to_detail_response(&product).await))
}

/// 更新商品（管理员）
pub async fn update(
    Path(id): Path<i32>,
    State(ctx): State<AppContext>,
    Json(payload): Json<UpdateProduct>,
) -> Result<Json<ProductDetailResponse>> {
    let product = Product::find_by_id(id).one(&ctx.db).await?;

    if let Some(product) = product {
        let mut product: ActiveModel = product.into();

        if let Some(name) = payload.name {
            product.name = Set(name);
        }

        if let Some(description) = payload.description {
            product.description = Set(description);
        }

        if let Some(price) = payload.price {
            product.price = Set(price);
        }

        if let Some(image_urls) = payload.image_urls {
            product.image_urls = Set(serde_json::to_value(image_urls)?);
        }

        if let Some(details) = payload.details {
            product.details = Set(details);
        }

        if let Some(is_active) = payload.is_active {
            product.is_active = Set(is_active);
        }

        let product = product.update(&ctx.db).await?;
        Ok(Json(model_to_detail_response(&product).await))
    } else {
        Err(Error::string(format!("商品不存在: {}", id).as_str()))
    }
}

/// 删除商品（管理员）
pub async fn delete(
    Path(id): Path<i32>,
    State(ctx): State<AppContext>,
) -> Result<Json<serde_json::Value>> {
    let product = Product::find_by_id(id).one(&ctx.db).await?;

    if let Some(product) = product {
        let mut product: ActiveModel = product.into();
        product.is_active = Set(false);
        product.update(&ctx.db).await?;

        Ok(Json(json!({ "success": true, "message": "商品已删除" })))
    } else {
        Err(Error::string(format!("商品不存在: {}", id).as_str()))
    }
}

/// 物理删除商品（管理员）
pub async fn hard_delete(
    Path(id): Path<i32>,
    State(ctx): State<AppContext>,
) -> Result<Json<serde_json::Value>> {
    // 先检查商品是否存在
    let product = Product::find_by_id(id).one(&ctx.db).await?;

    if product.is_none() {
        return Err(Error::string(format!("商品不存在: {}", id).as_str()));
    }

    let _res = Product::delete_by_id(id).exec(&ctx.db).await?;

    Ok(Json(
        json!({ "success": true, "message": "商品已永久删除" }),
    ))
}

pub fn routes() -> Routes {
    Routes::new()
        .prefix("/api/products")
        .add("/", axum::routing::get(list))
        .add("/{id}", axum::routing::get(get))
        .add("/", axum::routing::post(create))
        .add("/{id}", axum::routing::put(update))
        .add("/{id}", axum::routing::delete(delete))
        .add("/{id}/hard", axum::routing::delete(hard_delete))
}
