<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { useToast } from 'primevue/usetoast';

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const toast = useToast();

const loading = ref(true);
const success = ref(false);
const error = ref('');

// 从URL中获取token
const token = route.params.token;

onMounted(async () => {
  try {
    await authStore.loginWithMagicLink(token);
    success.value = true;
    
    toast.add({
      severity: 'success',
      summary: '登录成功',
      detail: '您已成功登录！',
      life: 5000
    });
    
    // 登录成功后3秒跳转到首页
    setTimeout(() => {
      router.push('/');
    }, 3000);
  } catch (err) {
    error.value = err.message || '登录失败，请稍后再试';
    
    toast.add({
      severity: 'error',
      summary: '登录失败',
      detail: error.value,
      life: 5000
    });
  } finally {
    loading.value = false;
  }
});
</script>

<template>
  <Toast position="top-right" />
  
  <div class="magic-link-login-container">
    <Card class="magic-link-card">
      <template #title>
        <h1 class="text-center">魔法链接登录</h1>
      </template>
      
      <template #content>
        <div v-if="loading" class="loading-message">
          <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="4" />
          <p>正在处理您的登录请求...</p>
        </div>
        
        <div v-else-if="success" class="success-message">
          <i class="pi pi-check-circle" style="font-size: 3rem; color: var(--green-500);"></i>
          <h2>登录成功！</h2>
          <p>您已成功登录到您的账户。</p>
          <p class="redirect-message">即将跳转到首页...</p>
        </div>
        
        <div v-else class="error-message">
          <i class="pi pi-times-circle" style="font-size: 3rem; color: var(--red-500);"></i>
          <h2>登录失败</h2>
          <p>{{ error }}</p>
          <div class="form-actions">
            <Button 
              label="返回登录" 
              icon="pi pi-sign-in" 
              severity="primary" 
              @click="router.push('/login')"
            />
          </div>
        </div>
      </template>
    </Card>
  </div>
</template>

<style scoped>
.magic-link-login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 2rem;
}

.magic-link-card {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.magic-link-card :deep(.p-card-title) {
  padding-bottom: 0;
}

.magic-link-card :deep(.p-card-content) {
  padding-top: 1rem;
}

.text-center {
  text-align: center;
}

h1 {
  font-size: 1.75rem;
  color: var(--primary-color);
  margin: 0;
  padding: 0;
}

.loading-message,
.success-message,
.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
  text-align: center;
}

.loading-message p,
.success-message p,
.error-message p {
  margin: 1rem 0;
  font-size: 1.1rem;
  color: var(--text-color);
}

.success-message h2,
.error-message h2 {
  margin: 1rem 0;
  color: var(--text-color);
}

.redirect-message {
  font-size: 0.9rem;
  color: var(--text-color-secondary);
  margin-top: 1rem;
}

.form-actions {
  margin-top: 1.5rem;
}

@media (max-width: 576px) {
  .magic-link-login-container {
    padding: 1rem;
  }
  
  .magic-link-card {
    max-width: 100%;
  }
}
</style>
