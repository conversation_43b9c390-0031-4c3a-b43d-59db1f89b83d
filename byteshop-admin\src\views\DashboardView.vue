<script setup>
import { ref, onMounted } from 'vue';
import { useAuthStore } from '@/stores/auth';

const authStore = useAuthStore();
const user = ref(null);

onMounted(async () => {
  user.value = authStore.user;
});
</script>

<template>
  <div class="dashboard-container">
    <div class="grid">
      <div class="col-12">
        <div class="card">
          <h1>欢迎来到 ByteShop 管理后台</h1>
          <p v-if="user">您好，{{ user.name }}！</p>
          <p>这是管理员仪表盘页面。</p>
        </div>
      </div>
      
      <div class="col-12 md:col-6 lg:col-3">
        <div class="card">
          <div class="flex justify-content-between mb-3">
            <div>
              <span class="block text-500 font-medium mb-3">商品</span>
              <div class="text-900 font-medium text-xl">152</div>
            </div>
            <div class="flex align-items-center justify-content-center bg-blue-100 border-round" style="width: 2.5rem; height: 2.5rem">
              <i class="pi pi-shopping-cart text-blue-500 text-xl"></i>
            </div>
          </div>
          <span class="text-green-500 font-medium">24 新增 </span>
          <span class="text-500">自上次登录</span>
        </div>
      </div>
      
      <div class="col-12 md:col-6 lg:col-3">
        <div class="card">
          <div class="flex justify-content-between mb-3">
            <div>
              <span class="block text-500 font-medium mb-3">订单</span>
              <div class="text-900 font-medium text-xl">28</div>
            </div>
            <div class="flex align-items-center justify-content-center bg-orange-100 border-round" style="width: 2.5rem; height: 2.5rem">
              <i class="pi pi-inbox text-orange-500 text-xl"></i>
            </div>
          </div>
          <span class="text-green-500 font-medium">%52+ </span>
          <span class="text-500">增长</span>
        </div>
      </div>
      
      <div class="col-12 md:col-6 lg:col-3">
        <div class="card">
          <div class="flex justify-content-between mb-3">
            <div>
              <span class="block text-500 font-medium mb-3">用户</span>
              <div class="text-900 font-medium text-xl">84</div>
            </div>
            <div class="flex align-items-center justify-content-center bg-cyan-100 border-round" style="width: 2.5rem; height: 2.5rem">
              <i class="pi pi-users text-cyan-500 text-xl"></i>
            </div>
          </div>
          <span class="text-green-500 font-medium">12 新增 </span>
          <span class="text-500">本周</span>
        </div>
      </div>
      
      <div class="col-12 md:col-6 lg:col-3">
        <div class="card">
          <div class="flex justify-content-between mb-3">
            <div>
              <span class="block text-500 font-medium mb-3">收入</span>
              <div class="text-900 font-medium text-xl">¥15,000</div>
            </div>
            <div class="flex align-items-center justify-content-center bg-purple-100 border-round" style="width: 2.5rem; height: 2.5rem">
              <i class="pi pi-wallet text-purple-500 text-xl"></i>
            </div>
          </div>
          <span class="text-green-500 font-medium">¥1,200 </span>
          <span class="text-500">今日</span>
        </div>
      </div>
      
      <div class="col-12">
        <div class="card">
          <h2>快速操作</h2>
          <div class="grid">
            <div class="col-12 md:col-6 lg:col-3">
              <Button label="管理商品" icon="pi pi-shopping-cart" class="p-button-outlined w-full" @click="$router.push('/products')" />
            </div>
            <div class="col-12 md:col-6 lg:col-3">
              <Button label="添加商品" icon="pi pi-plus" class="p-button-outlined w-full" @click="$router.push('/products/create')" />
            </div>
            <div class="col-12 md:col-6 lg:col-3">
              <Button label="查看订单" icon="pi pi-inbox" class="p-button-outlined w-full" />
            </div>
            <div class="col-12 md:col-6 lg:col-3">
              <Button label="用户管理" icon="pi pi-users" class="p-button-outlined w-full" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dashboard-container {
  padding: 2rem;
}

.card {
  background: var(--surface-card);
  padding: 1.5rem;
  margin-bottom: 1rem;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

h1 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--text-color);
  font-size: 1.5rem;
}

h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: var(--text-color);
  font-size: 1.25rem;
}

.w-full {
  width: 100%;
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 1rem;
  }
}
</style>
