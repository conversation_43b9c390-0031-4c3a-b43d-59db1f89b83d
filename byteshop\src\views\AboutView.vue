<template>
  <div class="about-container">
    <div class="container">
      <Card>
        <template #title>
          <div class="card-title">
            <h1>关于我们</h1>
          </div>
        </template>
        <template #content>
          <div class="about-content">
            <section class="about-section">
              <h2>公司简介</h2>
              <p>ByteShop是一家专注于软件许可证销售的平台，我们致力于为客户提供安全、可靠的软件许可证服务。我们与多家知名软件厂商合作，确保您能够以最优惠的价格获得正版软件许可证。</p>
              <p>我们的团队由一群热爱技术、对软件行业充满热情的专业人士组成。我们相信，每一个客户都应该享受到便捷、安全的软件购买体验。</p>
            </section>

            <Divider />

            <section class="mission-section">
              <h2>我们的使命</h2>
              <p>我们的使命是简化软件许可证的购买过程，使其变得安全、透明和便捷。我们致力于：</p>
              <ul>
                <li>提供安全可靠的软件许可证</li>
                <li>确保客户获得最优惠的价格</li>
                <li>提供专业的技术支持和客户服务</li>
                <li>简化软件许可证的管理和使用过程</li>
              </ul>
            </section>

            <Divider />

            <section class="contact-section">
              <h2>联系我们</h2>
              <div class="contact-info">
                <div class="contact-item">
                  <i class="pi pi-envelope"></i>
                  <p><EMAIL></p>
                </div>
                <div class="contact-item">
                  <i class="pi pi-phone"></i>
                  <p>+86 123 4567 8910</p>
                </div>
                <div class="contact-item">
                  <i class="pi pi-map-marker"></i>
                  <p>北京市海淀区中关村软件园123号</p>
                </div>
              </div>
            </section>
          </div>
        </template>
      </Card>
    </div>
  </div>
</template>

<style scoped>
.about-container {
  padding: 2rem 0;
}

.card-title {
  text-align: center;
  margin-bottom: 1rem;
}

.card-title h1 {
  color: var(--primary-color);
  font-size: 2rem;
  margin: 0;
}

.about-content {
  color: var(--text-color);
}

.about-section,
.mission-section,
.contact-section {
  margin-bottom: 1.5rem;
}

h2 {
  color: var(--text-color);
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

p {
  line-height: 1.6;
  margin-bottom: 1rem;
}

ul {
  list-style-type: none;
  padding-left: 1rem;
  margin-bottom: 1rem;
}

ul li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

ul li:before {
  content: '\2713';
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

.contact-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.contact-item i {
  color: var(--primary-color);
  font-size: 1.2rem;
}

.contact-item p {
  margin: 0;
}

@media (max-width: 768px) {
  .contact-info {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}
</style>
