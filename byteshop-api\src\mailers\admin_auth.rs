// admin auth mailer
#![allow(non_upper_case_globals)]

use loco_rs::prelude::*;
use serde_json::json;

use crate::models::admins;

static welcome: Dir<'_> = include_dir!("src/mailers/admin_auth/welcome");
static forgot: Dir<'_> = include_dir!("src/mailers/admin_auth/forgot");
static magic_link: Dir<'_> = include_dir!("src/mailers/admin_auth/magic_link");

#[allow(clippy::module_name_repetitions)]
pub struct AdminAuthMailer {}
impl Mailer for AdminAuthMailer {}
impl AdminAuthMailer {
    /// Sending welcome email the the given admin
    ///
    /// # Errors
    ///
    /// When email sending is failed
    pub async fn send_welcome(ctx: &AppContext, admin: &admins::Model) -> Result<()> {
        Self::mail_template(
            ctx,
            &welcome,
            mailer::Args {
                to: admin.email.to_string(),
                locals: json!({
                  "name": admin.name,
                  "verifyToken": admin.email_verification_token,
                  "domain": ctx.config.server.full_url()
                }),
                ..Default::default()
            },
        )
        .await?;

        Ok(())
    }

    /// Sending forgot password email
    ///
    /// # Errors
    ///
    /// When email sending is failed
    pub async fn forgot_password(ctx: &AppContext, admin: &admins::Model) -> Result<()> {
        Self::mail_template(
            ctx,
            &forgot,
            mailer::Args {
                to: admin.email.to_string(),
                locals: json!({
                  "name": admin.name,
                  "resetToken": admin.reset_token,
                  "domain": ctx.config.server.full_url()
                }),
                ..Default::default()
            },
        )
        .await?;

        Ok(())
    }

    /// Sends a magic link authentication email to the admin.
    ///
    /// # Errors
    ///
    /// When email sending is failed
    pub async fn send_magic_link(ctx: &AppContext, admin: &admins::Model) -> Result<()> {
        Self::mail_template(
            ctx,
            &magic_link,
            mailer::Args {
                to: admin.email.to_string(),
                locals: json!({
                  "name": admin.name,
                  "token": admin.magic_link_token.clone().ok_or_else(|| Error::string(
                            "the admin model not contains magic link token",
                    ))?,
                  "host": ctx.config.server.full_url()
                }),
                ..Default::default()
            },
        )
        .await?;

        Ok(())
    }
}
