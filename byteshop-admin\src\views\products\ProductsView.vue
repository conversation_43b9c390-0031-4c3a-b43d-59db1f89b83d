<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useToast } from 'primevue/usetoast';
import api from '@/services/api';

const router = useRouter();
const toast = useToast();

const products = ref([]);
const loading = ref(true);
const error = ref(null);

onMounted(async () => {
  await fetchProducts();
});

const fetchProducts = async () => {
  loading.value = true;
  try {
    const response = await api.products.getProducts();
    products.value = response || [];
  } catch (err) {
    error.value = err.message || '获取商品列表失败';
    toast.add({
      severity: 'error',
      summary: '获取失败',
      detail: error.value,
      life: 5000
    });
  } finally {
    loading.value = false;
  }
};

const handleEdit = (product) => {
  router.push(`/products/${product.id}/edit`);
};

const handleView = (product) => {
  router.push(`/products/${product.id}`);
};

const handleDelete = async (product) => {
  try {
    await api.products.deleteProduct(product.id);
    toast.add({
      severity: 'success',
      summary: '删除成功',
      detail: '商品已成功删除',
      life: 3000
    });
    await fetchProducts();
  } catch (err) {
    toast.add({
      severity: 'error',
      summary: '删除失败',
      detail: err.message || '删除商品失败',
      life: 5000
    });
  }
};

const formatPrice = (price) => {
  return `¥${price.toFixed(2)}`;
};
</script>

<template>
  <div class="products-container">
    <div class="card">
      <div class="header">
        <h1>商品管理</h1>
        <Button 
          label="添加商品" 
          icon="pi pi-plus" 
          @click="router.push('/products/create')" 
          severity="success"
        />
      </div>
      
      <div v-if="loading" class="loading-container">
        <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="4" />
        <p>加载中...</p>
      </div>
      
      <div v-else-if="error" class="error-message">
        <Message severity="error">{{ error }}</Message>
      </div>
      
      <div v-else-if="products.length === 0" class="empty-message">
        <Message severity="info">暂无商品数据</Message>
        <div class="mt-3">
          <Button 
            label="添加第一个商品" 
            icon="pi pi-plus" 
            @click="router.push('/products/create')" 
          />
        </div>
      </div>
      
      <div v-else>
        <DataTable 
          :value="products" 
          :paginator="true" 
          :rows="10"
          :rowsPerPageOptions="[5, 10, 20, 50]"
          paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
          currentPageReportTemplate="显示第 {first} 到第 {last} 条，共 {totalRecords} 条"
          responsiveLayout="scroll"
          stripedRows
          class="p-datatable-sm"
        >
          <Column field="id" header="ID" style="width: 5%"></Column>
          <Column field="name" header="商品名称" style="width: 20%"></Column>
          <Column field="description" header="描述" style="width: 30%">
            <template #body="slotProps">
              <div class="description-cell">{{ slotProps.data.description }}</div>
            </template>
          </Column>
          <Column field="price" header="价格" style="width: 10%">
            <template #body="slotProps">
              {{ formatPrice(slotProps.data.price) }}
            </template>
          </Column>
          <Column field="stock" header="库存" style="width: 10%"></Column>
          <Column header="操作" style="width: 25%">
            <template #body="slotProps">
              <div class="action-buttons">
                <Button 
                  icon="pi pi-eye" 
                  severity="info" 
                  text 
                  rounded 
                  @click="handleView(slotProps.data)" 
                  tooltip="查看详情"
                  tooltipOptions="{ position: 'top' }"
                />
                <Button 
                  icon="pi pi-pencil" 
                  severity="success" 
                  text 
                  rounded 
                  @click="handleEdit(slotProps.data)" 
                  tooltip="编辑"
                  tooltipOptions="{ position: 'top' }"
                />
                <Button 
                  icon="pi pi-trash" 
                  severity="danger" 
                  text 
                  rounded 
                  @click="handleDelete(slotProps.data)" 
                  tooltip="删除"
                  tooltipOptions="{ position: 'top' }"
                />
              </div>
            </template>
          </Column>
        </DataTable>
      </div>
    </div>
  </div>
</template>

<style scoped>
.products-container {
  padding: 2rem;
}

.card {
  background: var(--surface-card);
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

h1 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--text-color);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
}

.loading-container p {
  margin-top: 1rem;
  color: var(--text-color-secondary);
}

.empty-message {
  text-align: center;
  padding: 2rem 0;
}

.mt-3 {
  margin-top: 1rem;
}

.description-cell {
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

@media (max-width: 768px) {
  .products-container {
    padding: 1rem;
  }
  
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
}
</style>
