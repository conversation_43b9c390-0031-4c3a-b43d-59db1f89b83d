<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { useToast } from 'primevue/usetoast';

const router = useRouter();
const authStore = useAuthStore();
const toast = useToast();

const form = ref({
  name: '',
  email: '',
  password: '',
  confirmPassword: '',
});

const errors = ref({});
const loading = ref(false);

const validateForm = () => {
  errors.value = {};
  let isValid = true;

  if (!form.value.name) {
    errors.value.name = '请输入管理员姓名';
    isValid = false;
  } else if (form.value.name.length < 2) {
    errors.value.name = '姓名长度至少为2个字符';
    isValid = false;
  }

  if (!form.value.email) {
    errors.value.email = '请输入电子邮箱';
    isValid = false;
  } else if (!/\S+@\S+\.\S+/.test(form.value.email)) {
    errors.value.email = '请输入有效的电子邮箱';
    isValid = false;
  }

  if (!form.value.password) {
    errors.value.password = '请输入密码';
    isValid = false;
  } else if (form.value.password.length < 8) {
    errors.value.password = '密码长度至少为8个字符';
    isValid = false;
  }

  if (form.value.password !== form.value.confirmPassword) {
    errors.value.confirmPassword = '两次输入的密码不一致';
    isValid = false;
  }

  return isValid;
};

const handleSubmit = async () => {
  if (!validateForm()) return;

  loading.value = true;
  try {
    await authStore.register({
      name: form.value.name,
      email: form.value.email,
      password: form.value.password,
    });
    
    toast.add({
      severity: 'success',
      summary: '注册成功',
      detail: '新管理员账户已创建，验证邮件已发送',
      life: 5000
    });
    
    // 清空表单
    form.value = {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
    };
    
    router.push('/dashboard');
  } catch (error) {
    errors.value.general = error.message || '注册失败';
    
    toast.add({
      severity: 'error',
      summary: '注册失败',
      detail: errors.value.general,
      life: 5000
    });
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <Toast position="top-right" />
  
  <div class="register-admin-container">
    <div class="card">
      <div class="header">
        <Button 
          icon="pi pi-arrow-left" 
          text 
          @click="router.push('/dashboard')" 
          class="back-button"
        />
        <h1>注册新管理员</h1>
      </div>
      
      <div v-if="errors.general" class="error-message">
        <Message severity="error">{{ errors.general }}</Message>
      </div>
      
      <form @submit.prevent="handleSubmit" class="register-form">
        <div class="field">
          <label for="name" class="form-label">管理员姓名 <span class="required">*</span></label>
          <span class="p-input-icon-left w-full">
            <i class="pi pi-user"></i>
            <InputText 
              id="name" 
              v-model="form.name" 
              placeholder="请输入管理员姓名" 
              :class="{'p-invalid': errors.name}" 
              class="w-full" 
              required 
            />
          </span>
          <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
        </div>
        
        <div class="field">
          <label for="email" class="form-label">电子邮箱 <span class="required">*</span></label>
          <span class="p-input-icon-left w-full">
            <i class="pi pi-envelope"></i>
            <InputText 
              id="email" 
              v-model="form.email" 
              type="email" 
              placeholder="请输入电子邮箱" 
              :class="{'p-invalid': errors.email}" 
              class="w-full" 
              required 
            />
          </span>
          <small v-if="errors.email" class="p-error">{{ errors.email }}</small>
        </div>
        
        <div class="field">
          <label for="password" class="form-label">密码 <span class="required">*</span></label>
          <span class="p-input-icon-left w-full">
            <i class="pi pi-lock"></i>
            <Password 
              id="password" 
              v-model="form.password" 
              placeholder="请输入密码" 
              :class="{'p-invalid': errors.password}" 
              :feedback="true"
              toggleMask
              inputClass="w-full" 
              class="w-full" 
              required 
            />
          </span>
          <small v-if="errors.password" class="p-error">{{ errors.password }}</small>
        </div>
        
        <div class="field">
          <label for="confirmPassword" class="form-label">确认密码 <span class="required">*</span></label>
          <span class="p-input-icon-left w-full">
            <i class="pi pi-lock-open"></i>
            <Password 
              id="confirmPassword" 
              v-model="form.confirmPassword" 
              placeholder="请再次输入密码" 
              :class="{'p-invalid': errors.confirmPassword}" 
              :feedback="false"
              toggleMask
              inputClass="w-full" 
              class="w-full" 
              required 
            />
          </span>
          <small v-if="errors.confirmPassword" class="p-error">{{ errors.confirmPassword }}</small>
        </div>
        
        <div class="info-message">
          <Message severity="info">
            新管理员注册后将收到验证邮件，需要验证邮箱后才能登录系统。
          </Message>
        </div>
        
        <div class="form-actions">
          <Button 
            type="button" 
            label="取消" 
            icon="pi pi-times" 
            severity="secondary" 
            outlined
            @click="router.push('/dashboard')" 
            class="mr-2"
            :disabled="loading"
          />
          <Button 
            type="submit" 
            label="注册管理员" 
            icon="pi pi-user-plus" 
            severity="primary" 
            :loading="loading" 
            :disabled="loading"
          />
        </div>
      </form>
    </div>
  </div>
</template>

<style scoped>
.register-admin-container {
  padding: 2rem;
}

.card {
  background: var(--surface-card);
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  max-width: 600px;
  margin: 0 auto;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
}

.back-button {
  margin-right: 1rem;
}

h1 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--text-color);
}

.error-message {
  margin-bottom: 1.5rem;
}

.info-message {
  margin-bottom: 1.5rem;
}

.register-form {
  width: 100%;
}

.field {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.required {
  color: var(--red-500);
}

.w-full {
  width: 100%;
}

:deep(.p-password) {
  width: 100%;
}

:deep(.p-password-input) {
  width: 100%;
}

.p-error {
  color: var(--red-500);
  margin-top: 0.25rem;
  font-size: 0.875rem;
  display: block;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 2rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

@media (max-width: 768px) {
  .register-admin-container {
    padding: 1rem;
  }
  
  .form-actions {
    flex-direction: column-reverse;
  }
  
  .form-actions .p-button {
    margin-right: 0;
    margin-bottom: 0.5rem;
    width: 100%;
  }
}
</style>
