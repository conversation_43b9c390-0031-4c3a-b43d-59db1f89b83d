import { createApp } from 'vue';
import { createPinia } from 'pinia';
import PrimeVue from 'primevue/config'
import Aura from '@primeuix/themes/aura';

// PrimeVue 组件
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import InputNumber from 'primevue/inputnumber';
import Password from 'primevue/password';
import Textarea from 'primevue/textarea';
import Card from 'primevue/card';
import Divider from 'primevue/divider';
import Message from 'primevue/message';
import Toast from 'primevue/toast';
import ToastService from 'primevue/toastservice';
import ProgressSpinner from 'primevue/progressspinner';
import Menu from 'primevue/menu';
import PanelMenu from 'primevue/panelmenu';
import Avatar from 'primevue/avatar';
import Tag from 'primevue/tag';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';

// 自定义组件
import App from './App.vue';
import AppLayout from './components/AppLayout.vue';
import router from './router';

const app = createApp(App);
const pinia = createPinia();

// 使用 Pinia
app.use(pinia);

// 使用 PrimeVue
app.use(PrimeVue, {
    theme: {
        preset: Aura
    }
});
app.use(ToastService);

// 注册 PrimeVue 组件
app.component('Button', Button);
app.component('InputText', InputText);
app.component('InputNumber', InputNumber);
app.component('Password', Password);
app.component('Textarea', Textarea);
app.component('Card', Card);
app.component('Divider', Divider);
app.component('Message', Message);
app.component('Toast', Toast);
app.component('ProgressSpinner', ProgressSpinner);
app.component('Menu', Menu);
app.component('PanelMenu', PanelMenu);
app.component('Avatar', Avatar);
app.component('Tag', Tag);
app.component('DataTable', DataTable);
app.component('Column', Column);

// 注册自定义组件
app.component('AppLayout', AppLayout);

// 使用路由
app.use(router);

// 挂载应用
app.mount('#app');
