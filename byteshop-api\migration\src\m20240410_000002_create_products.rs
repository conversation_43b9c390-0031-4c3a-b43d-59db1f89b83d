use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .create_table(
                Table::create()
                    .table(Products::Table)
                    .if_not_exists()
                    .col(
                        ColumnDef::new(Products::Id)
                            .integer()
                            .not_null()
                            .auto_increment()
                            .primary_key(),
                    )
                    .col(ColumnDef::new(Products::Name).string().not_null())
                    .col(ColumnDef::new(Products::Description).string().not_null())
                    .col(ColumnDef::new(Products::Price).double().not_null())
                    .col(ColumnDef::new(Products::ImageUrls).json().not_null())
                    .col(
                        ColumnDef::new(Products::IsActive)
                            .boolean()
                            .not_null()
                            .default(true),
                    )
                    .col(ColumnDef::new(Products::Details).text().not_null())
                    .col(ColumnDef::new(Products::CreatedAt).timestamp().not_null())
                    .col(ColumnDef::new(Products::UpdatedAt).timestamp().not_null())
                    .to_owned(),
            )
            .await
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(Products::Table).to_owned())
            .await
    }
}

/// 商品表的列定义
#[derive(Iden)]
enum Products {
    Table,
    Id,
    Name,
    Description,
    Price,
    ImageUrls,
    IsActive,
    Details,
    CreatedAt,
    UpdatedAt,
}
