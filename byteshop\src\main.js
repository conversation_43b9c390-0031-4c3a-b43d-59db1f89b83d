import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import PrimeVue from 'primevue/config'
import Aura from '@primeuix/themes/aura';

// PrimeVue组件
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import Toast from 'primevue/toast'
import ToastService from 'primevue/toastservice'
import Card from 'primevue/card'
import Divider from 'primevue/divider'
import Menu from 'primevue/menu'
import Dialog from 'primevue/dialog'
import Avatar from 'primevue/avatar'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import ConfirmDialog from 'primevue/confirmdialog'
import ConfirmationService from 'primevue/confirmationservice'
import Message from 'primevue/message'
import Password from 'primevue/password'
import ProgressSpinner from 'primevue/progressspinner'
// 注意：Dropdown和Calendar组件在当前版本中已弃用，但我们保留导入以备后用
import Checkbox from 'primevue/checkbox'
import RadioButton from 'primevue/radiobutton'

import App from './App.vue'
import router from './router'

const app = createApp(App)

// 使用Pinia
app.use(createPinia())

// 使用Router
app.use(router)

// 使用PrimeVue
app.use(PrimeVue, {
    theme: {
        preset: Aura
    }
})
app.use(ToastService)
app.use(ConfirmationService)

// 注册PrimeVue组件
app.component('Button', Button)
app.component('InputText', InputText)
app.component('Toast', Toast)
app.component('Card', Card)
app.component('Divider', Divider)
app.component('Menu', Menu)
app.component('Dialog', Dialog)
app.component('Avatar', Avatar)
app.component('DataTable', DataTable)
app.component('Column', Column)
app.component('ConfirmDialog', ConfirmDialog)
app.component('Message', Message)
app.component('Password', Password)
app.component('ProgressSpinner', ProgressSpinner)
// 暂时不注册已弃用的组件
// app.component('Dropdown', Dropdown)
// app.component('Calendar', Calendar)
app.component('Checkbox', Checkbox)
app.component('RadioButton', RadioButton)

app.mount('#app')
