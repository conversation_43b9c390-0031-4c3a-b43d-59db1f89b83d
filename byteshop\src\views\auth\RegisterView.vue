<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { useToast } from 'primevue/usetoast';

const router = useRouter();
const authStore = useAuthStore();
const toast = useToast();

const form = ref({
  name: '',
  email: '',
  password: '',
  passwordConfirmation: '',
});

const errors = ref({});
const successMessage = ref('');
const loading = ref(false);

const validateForm = () => {
  errors.value = {};
  let isValid = true;

  if (!form.value.name) {
    errors.value.name = '请输入姓名';
    isValid = false;
  }

  if (!form.value.email) {
    errors.value.email = '请输入电子邮箱';
    isValid = false;
  } else if (!/\S+@\S+\.\S+/.test(form.value.email)) {
    errors.value.email = '请输入有效的电子邮箱';
    isValid = false;
  }

  if (!form.value.password) {
    errors.value.password = '请输入密码';
    isValid = false;
  } else if (form.value.password.length < 8) {
    errors.value.password = '密码长度至少为8个字符';
    isValid = false;
  }

  if (form.value.password !== form.value.passwordConfirmation) {
    errors.value.passwordConfirmation = '两次输入的密码不一致';
    isValid = false;
  }

  return isValid;
};

const handleSubmit = async () => {
  if (!validateForm()) return;

  loading.value = true;
  try {
    // 添加password_confirmation字段
    const response = await authStore.register({
      name: form.value.name,
      email: form.value.email,
      password: form.value.password,
      password_confirmation: form.value.passwordConfirmation,
    });

    // 注册成功
    if (response && (response.success || response.user)) {
      successMessage.value = '注册成功！请检查您的邮箱以验证账户。';

      toast.add({
        severity: 'success',
        summary: '注册成功',
        detail: '请检查您的邮箱以验证账户',
        life: 5000
      });

      // 清空表单
      form.value = {
        name: '',
        email: '',
        password: '',
        passwordConfirmation: '',
      };

      // 注册成功后跳转到登录页
      setTimeout(() => {
        router.push('/login');
      }, 3000);
    }
  } catch (error) {
    if (error.message) {
      errors.value.general = error.message;
      toast.add({
        severity: 'error',
        summary: '注册失败',
        detail: error.message,
        life: 5000
      });
    } else {
      errors.value.general = '注册失败，请稍后再试';
      toast.add({
        severity: 'error',
        summary: '注册失败',
        detail: '请稍后再试',
        life: 5000
      });
    }
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <Toast position="top-right" />

  <div class="register-container">
    <Card class="register-card">
      <template #title>
        <h1 class="text-center">注册账户</h1>
      </template>

      <template #content>
        <div v-if="successMessage" class="success-message">
          <Message severity="success">{{ successMessage }}</Message>
        </div>

        <div v-else>
          <div v-if="errors.general" class="error-message">
            <Message severity="error">{{ errors.general }}</Message>
          </div>

          <form @submit.prevent="handleSubmit" class="register-form">
            <div class="field">
              <label for="name" class="form-label">姓名</label>
              <span class="p-input-icon-left w-full">
                <i class="pi pi-user"></i>
                <InputText
                  id="name"
                  v-model="form.name"
                  placeholder="请输入您的姓名"
                  :class="{'p-invalid': errors.name}"
                  class="w-full"
                  required
                />
              </span>
              <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
            </div>

            <div class="field">
              <label for="email" class="form-label">电子邮箱</label>
              <span class="p-input-icon-left w-full">
                <i class="pi pi-envelope"></i>
                <InputText
                  id="email"
                  v-model="form.email"
                  type="email"
                  placeholder="请输入您的电子邮箱"
                  :class="{'p-invalid': errors.email}"
                  class="w-full"
                  required
                />
              </span>
              <small v-if="errors.email" class="p-error">{{ errors.email }}</small>
            </div>

            <div class="field">
              <label for="password" class="form-label">密码</label>
              <span class="p-input-icon-left w-full">
                <i class="pi pi-lock"></i>
                <Password
                  id="password"
                  v-model="form.password"
                  placeholder="请输入密码"
                  :class="{'p-invalid': errors.password}"
                  :feedback="true"
                  toggleMask
                  inputClass="w-full"
                  class="w-full"
                  required
                />
              </span>
              <small v-if="errors.password" class="p-error">{{ errors.password }}</small>
            </div>

            <div class="field">
              <label for="passwordConfirmation" class="form-label">确认密码</label>
              <span class="p-input-icon-left w-full">
                <i class="pi pi-lock-open"></i>
                <Password
                  id="passwordConfirmation"
                  v-model="form.passwordConfirmation"
                  placeholder="请再次输入密码"
                  :class="{'p-invalid': errors.passwordConfirmation}"
                  :feedback="false"
                  toggleMask
                  inputClass="w-full"
                  class="w-full"
                  required
                />
              </span>
              <small v-if="errors.passwordConfirmation" class="p-error">{{ errors.passwordConfirmation }}</small>
            </div>

            <div class="form-actions">
              <Button
                type="submit"
                label="注册"
                icon="pi pi-user-plus"
                :loading="loading"
                :disabled="loading"
                severity="primary"
                raised
                class="w-full"
              />
            </div>

            <div class="form-footer">
              已有账户？
              <router-link to="/login">登录</router-link>
            </div>
          </form>
        </div>
      </template>
    </Card>
  </div>
</template>

<style scoped>
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 2rem;
}

.register-card {
  width: 100%;
  max-width: 450px;
  margin: 0 auto;
}

.register-card :deep(.p-card-title) {
  padding-bottom: 0;
}

.register-card :deep(.p-card-content) {
  padding-top: 1rem;
}

.register-form {
  width: 100%;
}

.field {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.w-full {
  width: 100%;
}

:deep(.p-password) {
  width: 100%;
}

:deep(.p-password-input) {
  width: 100%;
}

h1 {
  font-size: 1.75rem;
  color: var(--primary-color);
  margin: 0;
  padding: 0;
}

.text-center {
  text-align: center;
}

.success-message,
.error-message {
  margin-bottom: 1.5rem;
}

.p-error {
  color: var(--red-500);
  margin-top: 0.25rem;
  font-size: 0.875rem;
  display: block;
}

.form-actions {
  margin: 1.5rem 0;
}

.form-footer {
  text-align: center;
  margin-top: 1.5rem;
  color: var(--text-color-secondary);
  font-size: 0.875rem;
}

.form-footer a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.form-footer a:hover {
  text-decoration: underline;
}

@media (max-width: 576px) {
  .register-container {
    padding: 1rem;
  }

  .register-card {
    max-width: 100%;
  }
}
</style>
