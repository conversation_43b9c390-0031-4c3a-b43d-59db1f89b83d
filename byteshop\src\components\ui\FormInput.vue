<script setup>
import { computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: '',
  },
  label: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: 'text',
  },
  placeholder: {
    type: String,
    default: '',
  },
  required: {
    type: Boolean,
    default: false,
  },
  error: {
    type: String,
    default: '',
  },
  icon: {
    type: String,
    default: '',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:modelValue']);

const updateValue = (event) => {
  emit('update:modelValue', event.target.value);
};

const inputClasses = computed(() => {
  return {
    'p-invalid': !!props.error,
  };
});
</script>

<template>
  <div class="form-field">
    <label v-if="label" :for="label" class="form-label">{{ label }}</label>

    <span v-if="icon" class="p-input-icon-left">
      <i :class="`pi pi-${icon}`"></i>
      <InputText
        :id="label"
        :type="type"
        :value="modelValue"
        :placeholder="placeholder"
        :required="required"
        :class="inputClasses"
        :disabled="disabled"
        class="w-full"
        @input="updateValue"
      />
    </span>

    <InputText
      v-else
      :id="label"
      :type="type"
      :value="modelValue"
      :placeholder="placeholder"
      :required="required"
      :class="inputClasses"
      :disabled="disabled"
      class="w-full"
      @input="updateValue"
    />

    <small v-if="error" class="p-error">{{ error }}</small>
  </div>
</template>

<style scoped>
.form-field {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.p-error {
  color: var(--red-500);
  margin-top: 0.25rem;
  font-size: 0.875rem;
  display: block;
}

/* 确保输入框宽度为100% */
:deep(.p-inputtext) {
  width: 100%;
}
</style>
