<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();
const productId = route.params.id;

const product = ref(null);
const loading = ref(true);
const error = ref(null);

// 模拟产品数据
const mockProducts = [
  {
    id: 1,
    name: '软件开发工具套件',
    description: '专业的软件开发工具套件，包含代码编辑器、调试器等多种工具。',
    price: 299.99,
    image: 'https://via.placeholder.com/600x400?text=软件开发工具',
    details: `
      <p>这是一款专业的软件开发工具套件，为开发者提供了全面的开发环境。</p>
      <h3>主要功能：</h3>
      <ul>
        <li>智能代码编辑器，支持多种编程语言</li>
        <li>强大的调试工具，帮助快速定位问题</li>
        <li>内置版本控制系统，方便团队协作</li>
        <li>代码分析工具，提高代码质量</li>
        <li>自动化构建和测试工具</li>
      </ul>
      <h3>系统要求：</h3>
      <ul>
        <li>操作系统：Windows 10/11, macOS 10.15+, Linux</li>
        <li>处理器：Intel Core i5 或同等性能</li>
        <li>内存：8GB RAM（推荐16GB）</li>
        <li>存储空间：至少2GB可用空间</li>
      </ul>
    `,
    license_types: [
      { id: 1, name: '日卡', duration: '1天', price: 9.99 },
      { id: 2, name: '周卡', duration: '7天', price: 49.99 },
      { id: 3, name: '月卡', duration: '30天', price: 149.99 },
      { id: 4, name: '年卡', duration: '365天', price: 299.99 },
    ]
  },
  {
    id: 2,
    name: '数据分析软件',
    description: '强大的数据分析软件，支持多种数据格式和分析方法。',
    price: 199.99,
    image: 'https://via.placeholder.com/600x400?text=数据分析软件',
    details: `
      <p>这是一款功能强大的数据分析软件，可以帮助您快速处理和分析各种数据。</p>
      <h3>主要功能：</h3>
      <ul>
        <li>支持多种数据格式导入和导出</li>
        <li>强大的数据清洗和预处理功能</li>
        <li>丰富的统计分析方法</li>
        <li>直观的数据可视化工具</li>
        <li>机器学习模型构建和评估</li>
      </ul>
      <h3>系统要求：</h3>
      <ul>
        <li>操作系统：Windows 10/11, macOS 10.15+, Linux</li>
        <li>处理器：Intel Core i7 或同等性能</li>
        <li>内存：16GB RAM（推荐32GB）</li>
        <li>存储空间：至少5GB可用空间</li>
      </ul>
    `,
    license_types: [
      { id: 1, name: '日卡', duration: '1天', price: 6.99 },
      { id: 2, name: '周卡', duration: '7天', price: 39.99 },
      { id: 3, name: '月卡', duration: '30天', price: 99.99 },
      { id: 4, name: '年卡', duration: '365天', price: 199.99 },
    ]
  },
  {
    id: 3,
    name: '图形设计软件',
    description: '专业的图形设计软件，支持多种设计需求。',
    price: 249.99,
    image: 'https://via.placeholder.com/600x400?text=图形设计软件',
    details: `
      <p>这是一款专业的图形设计软件，为设计师提供了丰富的创作工具。</p>
      <h3>主要功能：</h3>
      <ul>
        <li>矢量图形编辑和创建</li>
        <li>位图处理和编辑</li>
        <li>丰富的滤镜和特效</li>
        <li>专业的排版和文字处理</li>
        <li>支持多种设计格式导入导出</li>
      </ul>
      <h3>系统要求：</h3>
      <ul>
        <li>操作系统：Windows 10/11, macOS 10.15+</li>
        <li>处理器：Intel Core i5 或同等性能</li>
        <li>内存：8GB RAM（推荐16GB）</li>
        <li>显卡：支持OpenGL 3.3或更高版本</li>
        <li>存储空间：至少3GB可用空间</li>
      </ul>
    `,
    license_types: [
      { id: 1, name: '日卡', duration: '1天', price: 7.99 },
      { id: 2, name: '周卡', duration: '7天', price: 44.99 },
      { id: 3, name: '月卡', duration: '30天', price: 129.99 },
      { id: 4, name: '年卡', duration: '365天', price: 249.99 },
    ]
  },
  {
    id: 4,
    name: '办公软件套件',
    description: '包含文字处理、电子表格、演示文稿等办公软件。',
    price: 149.99,
    image: 'https://via.placeholder.com/600x400?text=办公软件套件',
    details: `
      <p>这是一款全面的办公软件套件，包含了日常办公所需的各种工具。</p>
      <h3>主要功能：</h3>
      <ul>
        <li>专业的文字处理软件</li>
        <li>功能强大的电子表格</li>
        <li>直观的演示文稿制作工具</li>
        <li>数据库管理系统</li>
        <li>电子邮件客户端</li>
      </ul>
      <h3>系统要求：</h3>
      <ul>
        <li>操作系统：Windows 8/10/11, macOS 10.14+, Linux</li>
        <li>处理器：Intel Core i3 或同等性能</li>
        <li>内存：4GB RAM（推荐8GB）</li>
        <li>存储空间：至少2GB可用空间</li>
      </ul>
    `,
    license_types: [
      { id: 1, name: '日卡', duration: '1天', price: 4.99 },
      { id: 2, name: '周卡', duration: '7天', price: 29.99 },
      { id: 3, name: '月卡', duration: '30天', price: 79.99 },
      { id: 4, name: '年卡', duration: '365天', price: 149.99 },
    ]
  },
];

onMounted(() => {
  // 模拟加载数据
  setTimeout(() => {
    const foundProduct = mockProducts.find(p => p.id === parseInt(productId));
    
    if (foundProduct) {
      product.value = foundProduct;
      loading.value = false;
    } else {
      error.value = '商品不存在';
      loading.value = false;
    }
  }, 1000);
});

const selectedLicense = ref(null);

const selectLicense = (license) => {
  selectedLicense.value = license;
};

const buyNow = () => {
  if (!selectedLicense.value) {
    alert('请先选择许可证类型');
    return;
  }
  
  // 这里可以跳转到购买页面或者打开购买对话框
  alert(`您选择了 ${product.value.name} 的 ${selectedLicense.value.name}，价格为 ¥${selectedLicense.value.price}`);
};

const goBack = () => {
  router.go(-1);
};
</script>

<template>
  <div class="product-detail-container">
    <div class="container">
      <Button icon="pi pi-arrow-left" text @click="goBack" class="back-button">返回</Button>
      
      <div v-if="loading" class="loading-container">
        <i class="pi pi-spin pi-spinner" style="font-size: 2rem"></i>
        <p>正在加载商品信息...</p>
      </div>
      
      <div v-else-if="error" class="error-container">
        <Card>
          <template #content>
            <div class="error-message">
              <i class="pi pi-exclamation-triangle" style="font-size: 2rem; color: var(--red-500);"></i>
              <h2>{{ error }}</h2>
              <Button label="返回首页" icon="pi pi-home" @click="router.push('/')" />
            </div>
          </template>
        </Card>
      </div>
      
      <div v-else class="product-detail">
        <Card>
          <template #content>
            <div class="product-layout">
              <div class="product-image-container">
                <img :src="product.image" :alt="product.name" class="product-image" />
              </div>
              
              <div class="product-info">
                <h1 class="product-title">{{ product.name }}</h1>
                <p class="product-description">{{ product.description }}</p>
                
                <Divider />
                
                <div class="license-selection">
                  <h2>选择许可证类型</h2>
                  
                  <div class="license-options">
                    <div 
                      v-for="license in product.license_types" 
                      :key="license.id"
                      class="license-option"
                      :class="{ 'selected': selectedLicense && selectedLicense.id === license.id }"
                      @click="selectLicense(license)"
                    >
                      <div class="license-info">
                        <h3>{{ license.name }}</h3>
                        <p>有效期: {{ license.duration }}</p>
                      </div>
                      <div class="license-price">
                        <span>¥{{ license.price.toFixed(2) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="product-actions">
                  <Button 
                    label="立即购买" 
                    icon="pi pi-shopping-cart" 
                    class="p-button-success p-button-lg" 
                    @click="buyNow"
                    :disabled="!selectedLicense"
                  />
                </div>
              </div>
            </div>
            
            <Divider />
            
            <div class="product-details">
              <h2>商品详情</h2>
              <div class="details-content" v-html="product.details"></div>
            </div>
          </template>
        </Card>
      </div>
    </div>
  </div>
</template>

<style scoped>
.product-detail-container {
  padding: 2rem 0;
}

.back-button {
  margin-bottom: 1rem;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
  text-align: center;
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.product-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.product-image-container {
  overflow: hidden;
  border-radius: 8px;
}

.product-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-image:hover {
  transform: scale(1.05);
}

.product-title {
  font-size: 1.8rem;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.product-description {
  color: var(--text-color-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.license-selection {
  margin: 1.5rem 0;
}

.license-selection h2 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: var(--text-color);
}

.license-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.license-option {
  border: 1px solid var(--surface-border);
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.license-option:hover {
  border-color: var(--primary-color);
  background-color: var(--primary-50);
}

.license-option.selected {
  border-color: var(--primary-color);
  background-color: var(--primary-50);
  box-shadow: 0 0 0 1px var(--primary-color);
}

.license-info h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  color: var(--text-color);
}

.license-info p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-color-secondary);
}

.license-price {
  font-weight: bold;
  font-size: 1.2rem;
  color: var(--primary-color);
}

.product-actions {
  margin-top: 2rem;
}

.product-details {
  margin-top: 2rem;
}

.product-details h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--text-color);
}

.details-content {
  color: var(--text-color);
  line-height: 1.6;
}

.details-content h3 {
  margin: 1.5rem 0 0.5rem 0;
  font-size: 1.2rem;
  color: var(--text-color);
}

.details-content ul {
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

.details-content li {
  margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
  .product-layout {
    grid-template-columns: 1fr;
  }
  
  .license-options {
    grid-template-columns: 1fr;
  }
}
</style>
