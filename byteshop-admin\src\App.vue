<script setup>
import { RouterView, useRoute } from 'vue-router';
import { ref, computed, watch, onMounted } from 'vue';
import { useAuthStore } from '@/stores/auth';

const authStore = useAuthStore();
const route = useRoute();

// 检查当前路由是否需要认证
const isAuthRoute = computed(() => {
  return route.meta.requiresAuth === true;
});

// 检查是否已登录
const isAuthenticated = computed(() => {
  return authStore.isAuthenticated;
});

// 获取当前用户信息
onMounted(async () => {
  if (authStore.token && !authStore.user) {
    try {
      await authStore.fetchCurrentUser();
    } catch (error) {
      console.error('Failed to fetch user:', error);
    }
  }
});
</script>

<template>
  <div class="app-container">
    <!-- 认证页面布局 -->
    <div v-if="!isAuthRoute" class="auth-layout">
      <RouterView />
    </div>

    <!-- 主应用布局 -->
    <div v-else-if="isAuthenticated" class="main-layout">
      <AppLayout>
        <RouterView />
      </AppLayout>
    </div>

    <!-- 加载中或未认证 -->
    <div v-else class="loading-container">
      <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="4" />
      <p>加载中...</p>
    </div>
  </div>
</template>

<style>

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  font-family: var(--font-family);
  background-color: var(--surface-ground);
  color: var(--text-color);
  height: 100%;
}

#app {
  height: 100%;
}

.app-container {
  height: 100%;
}

.auth-layout {
  height: 100%;
}

.main-layout {
  height: 100%;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.loading-container p {
  margin-top: 1rem;
  color: var(--text-color-secondary);
}
</style>
