<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { useToast } from 'primevue/usetoast';

const router = useRouter();
const authStore = useAuthStore();
const toast = useToast();

const form = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
});

const errors = ref({});
const loading = ref(false);

const validateForm = () => {
  errors.value = {};
  let isValid = true;

  if (!form.value.currentPassword) {
    errors.value.currentPassword = '请输入当前密码';
    isValid = false;
  }

  if (!form.value.newPassword) {
    errors.value.newPassword = '请输入新密码';
    isValid = false;
  } else if (form.value.newPassword.length < 8) {
    errors.value.newPassword = '新密码长度至少为8个字符';
    isValid = false;
  }

  if (form.value.newPassword !== form.value.confirmPassword) {
    errors.value.confirmPassword = '两次输入的密码不一致';
    isValid = false;
  }

  return isValid;
};

const handleSubmit = async () => {
  if (!validateForm()) return;

  loading.value = true;
  try {
    await authStore.changePassword({
      current_password: form.value.currentPassword,
      new_password: form.value.newPassword,
      confirm_password: form.value.confirmPassword,
    });
    
    toast.add({
      severity: 'success',
      summary: '密码修改成功',
      detail: '您的密码已成功修改',
      life: 3000
    });
    
    // 清空表单
    form.value = {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    };
    
    router.push('/profile');
  } catch (error) {
    errors.value.general = error.message || '修改密码失败';
    
    toast.add({
      severity: 'error',
      summary: '修改失败',
      detail: errors.value.general,
      life: 5000
    });
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <Toast position="top-right" />
  
  <div class="change-password-container">
    <div class="card">
      <div class="header">
        <Button 
          icon="pi pi-arrow-left" 
          text 
          @click="router.push('/profile')" 
          class="back-button"
        />
        <h1>修改密码</h1>
      </div>
      
      <div v-if="errors.general" class="error-message">
        <Message severity="error">{{ errors.general }}</Message>
      </div>
      
      <form @submit.prevent="handleSubmit" class="change-password-form">
        <div class="field">
          <label for="currentPassword" class="form-label">当前密码</label>
          <span class="p-input-icon-left w-full">
            <i class="pi pi-lock"></i>
            <Password 
              id="currentPassword" 
              v-model="form.currentPassword" 
              placeholder="请输入当前密码" 
              :class="{'p-invalid': errors.currentPassword}" 
              :feedback="false"
              toggleMask
              inputClass="w-full" 
              class="w-full" 
              required 
            />
          </span>
          <small v-if="errors.currentPassword" class="p-error">{{ errors.currentPassword }}</small>
        </div>
        
        <div class="field">
          <label for="newPassword" class="form-label">新密码</label>
          <span class="p-input-icon-left w-full">
            <i class="pi pi-key"></i>
            <Password 
              id="newPassword" 
              v-model="form.newPassword" 
              placeholder="请输入新密码" 
              :class="{'p-invalid': errors.newPassword}" 
              :feedback="true"
              toggleMask
              inputClass="w-full" 
              class="w-full" 
              required 
            />
          </span>
          <small v-if="errors.newPassword" class="p-error">{{ errors.newPassword }}</small>
        </div>
        
        <div class="field">
          <label for="confirmPassword" class="form-label">确认新密码</label>
          <span class="p-input-icon-left w-full">
            <i class="pi pi-lock-open"></i>
            <Password 
              id="confirmPassword" 
              v-model="form.confirmPassword" 
              placeholder="请再次输入新密码" 
              :class="{'p-invalid': errors.confirmPassword}" 
              :feedback="false"
              toggleMask
              inputClass="w-full" 
              class="w-full" 
              required 
            />
          </span>
          <small v-if="errors.confirmPassword" class="p-error">{{ errors.confirmPassword }}</small>
        </div>
        
        <div class="form-actions">
          <Button 
            type="button" 
            label="取消" 
            icon="pi pi-times" 
            severity="secondary" 
            outlined
            @click="router.push('/profile')" 
            class="mr-2"
            :disabled="loading"
          />
          <Button 
            type="submit" 
            label="修改密码" 
            icon="pi pi-check" 
            severity="primary" 
            :loading="loading" 
            :disabled="loading"
          />
        </div>
      </form>
    </div>
  </div>
</template>

<style scoped>
.change-password-container {
  padding: 2rem;
}

.card {
  background: var(--surface-card);
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  max-width: 500px;
  margin: 0 auto;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
}

.back-button {
  margin-right: 1rem;
}

h1 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--text-color);
}

.error-message {
  margin-bottom: 1.5rem;
}

.change-password-form {
  width: 100%;
}

.field {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.w-full {
  width: 100%;
}

:deep(.p-password) {
  width: 100%;
}

:deep(.p-password-input) {
  width: 100%;
}

.p-error {
  color: var(--red-500);
  margin-top: 0.25rem;
  font-size: 0.875rem;
  display: block;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 2rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

@media (max-width: 768px) {
  .change-password-container {
    padding: 1rem;
  }
  
  .form-actions {
    flex-direction: column-reverse;
  }
  
  .form-actions .p-button {
    margin-right: 0;
    margin-bottom: 0.5rem;
    width: 100%;
  }
}
</style>
