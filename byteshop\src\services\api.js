// API服务层，用于与后端API通信

// 使用相对路径，让Vite代理处理跨域问题
const API_URL = '/api';

// 通用请求方法
async function request(url, options = {}) {
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  // 如果有token，添加到请求头
  const token = localStorage.getItem('token');
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  const response = await fetch(`${API_URL}${url}`, {
    ...options,
    headers,
  });

  // 如果响应不成功，抛出错误
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `请求失败: ${response.status}`);
  }

  // 如果是魔法链接登录的响应，尝试获取token
  if (url.startsWith('/auth/magic-link/') && response.headers.has('authorization')) {
    const authHeader = response.headers.get('authorization');
    const token = authHeader.replace('Bearer ', '');
    console.log('Magic link token from header:', token);
    return { token, success: true };
  }

  // 如果响应是204 No Content或者是空的200，返回成功状态
  if (response.status === 204 || (response.status === 200 && (response.headers.get('content-length') === '0' || !response.headers.get('content-type')?.includes('application/json')))) {
    return { success: true };
  }

  // 尝试解析JSON数据
  try {
    const data = await response.json();
    // 如果是魔法链接登录的响应，并且有token字段
    if (url.startsWith('/auth/magic-link/') && data.token) {
      console.log('Magic link token from body:', data.token);
    }
    return data;
  } catch (error) {
    console.error('Failed to parse JSON:', error);
    // 如果无法解析JSON，返回成功状态
    return { success: true };
  }
}

// 用户相关API
export const authApi = {
  // 用户注册
  register: (userData) => {
    return request('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  },

  // 用户登录
  login: (credentials) => {
    return request('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  },

  // 获取当前用户信息
  getCurrentUser: () => {
    return request('/auth/current', {
      method: 'GET',
    });
  },

  // 忘记密码
  forgotPassword: (email) => {
    return request('/auth/forgot', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  },

  // 重置密码
  resetPassword: (resetData) => {
    return request('/auth/reset', {
      method: 'POST',
      body: JSON.stringify(resetData),
    });
  },

  // 验证邮箱
  verifyEmail: (token) => {
    return request(`/auth/verify/${token}`, {
      method: 'GET',
    });
  },

  // 魔法链接登录
  magicLink: (email) => {
    return request('/auth/magic-link', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  },

  // 使用魔法链接登录
  loginWithMagicLink: (token) => {
    return request(`/auth/magic-link/${token}`, {
      method: 'GET',
    });
  },
};

// 商品相关API
export const productsApi = {
  // 获取商品列表
  getProducts: () => {
    return request('/products', {
      method: 'GET',
    });
  },

  // 获取商品详情
  getProduct: (id) => {
    return request(`/products/${id}`, {
      method: 'GET',
    });
  },

  // 创建商品（管理员）
  createProduct: (productData) => {
    return request('/products', {
      method: 'POST',
      body: JSON.stringify(productData),
    });
  },

  // 更新商品（管理员）
  updateProduct: (id, productData) => {
    return request(`/products/${id}`, {
      method: 'PUT',
      body: JSON.stringify(productData),
    });
  },

  // 删除商品（管理员）
  deleteProduct: (id) => {
    return request(`/products/${id}`, {
      method: 'DELETE',
    });
  },
};

export default {
  auth: authApi,
  products: productsApi,
};
