<script setup>
import { RouterLink, RouterView } from 'vue-router'
import { computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// 计算属性：用户是否已登录
const isAuthenticated = computed(() => authStore.isAuthenticated)

// 在组件挂载时获取用户信息
onMounted(async () => {
  if (authStore.token && !authStore.user) {
    try {
      await authStore.fetchCurrentUser()
    } catch (error) {
      console.error('获取用户信息失败', error)
    }
  }
})

// 退出登录
const handleLogout = async () => {
  await authStore.logout()
  // 刷新页面以确保状态完全清除
  window.location.href = '/'
}
</script>

<template>
  <header>
    <div class="logo-container">
      <RouterLink to="/">
        <img alt="ByteShop Logo" class="logo" src="@/assets/logo.svg" width="40" height="40" />
        <span class="site-name">ByteShop</span>
      </RouterLink>
    </div>

    <nav>
      <RouterLink to="/">首页</RouterLink>
      <RouterLink to="/about">关于我们</RouterLink>

      <!-- 未登录用户显示 -->
      <template v-if="!isAuthenticated">
        <RouterLink to="/login" class="auth-link">登录</RouterLink>
        <RouterLink to="/register" class="auth-link">注册</RouterLink>
      </template>

      <!-- 已登录用户显示 -->
      <template v-else>
        <RouterLink to="/profile">个人中心</RouterLink>
        <a href="#" @click.prevent="handleLogout" class="logout-link">退出</a>
      </template>
    </nav>
  </header>

  <main>
    <RouterView />
  </main>

  <footer>
    <p>&copy; {{ new Date().getFullYear() }} ByteShop. 保留所有权利。</p>
  </footer>
</template>

<style scoped>
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo-container a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #333;
}

.logo {
  margin-right: 0.5rem;
}

.site-name {
  font-size: 1.5rem;
  font-weight: bold;
}

nav {
  display: flex;
  gap: 1.5rem;
}

nav a {
  color: #666;
  text-decoration: none;
  font-size: 1rem;
  transition: color 0.2s;
}

nav a:hover {
  color: #2196f3;
}

nav a.router-link-active {
  color: #2196f3;
  font-weight: 500;
}

.auth-link {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.auth-link:hover {
  background-color: #f5f5f5;
}

.logout-link {
  color: #f44336;
}

.logout-link:hover {
  color: #d32f2f;
}

main {
  min-height: calc(100vh - 140px);
  padding: 1rem;
}

footer {
  text-align: center;
  padding: 1.5rem;
  background-color: #f5f5f5;
  color: #666;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  header {
    flex-direction: column;
    padding: 1rem;
  }

  .logo-container {
    margin-bottom: 1rem;
  }

  nav {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
  }
}
</style>
