<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { useToast } from 'primevue/usetoast';

const router = useRouter();
const authStore = useAuthStore();
const toast = useToast();

const user = ref(null);
const loading = ref(false);

onMounted(async () => {
  loading.value = true;
  try {
    await authStore.fetchCurrentUser();
    user.value = authStore.user;
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: '获取用户信息失败',
      detail: error.message || '请稍后再试',
      life: 5000
    });
  } finally {
    loading.value = false;
  }
});

const handleLogout = async () => {
  try {
    await authStore.logout();
    toast.add({
      severity: 'success',
      summary: '退出成功',
      detail: '您已成功退出登录',
      life: 3000
    });
    // 跳转到登录页面
    router.push('/login');
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: '退出失败',
      detail: error.message || '退出登录时发生错误',
      life: 5000
    });
    // 即使出错也跳转到登录页面
    router.push('/login');
  }
};
</script>

<template>
  <Toast position="top-right" />

  <div class="profile-container">
    <div class="grid">
      <div class="col-12">
        <div class="card">
          <h1>个人资料</h1>

          <div v-if="loading" class="loading-container">
            <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="4" />
            <p>加载中...</p>
          </div>

          <div v-else-if="user" class="profile-content">
            <div class="grid">
              <div class="col-12 md:col-4">
                <div class="profile-avatar">
                  <Avatar :label="user.name.charAt(0)" size="xlarge" shape="circle" style="background-color: var(--primary-color); color: #ffffff" />
                </div>
              </div>

              <div class="col-12 md:col-8">
                <div class="profile-details">
                  <div class="field">
                    <label>姓名</label>
                    <div class="value">{{ user.name }}</div>
                  </div>

                  <div class="field">
                    <label>电子邮箱</label>
                    <div class="value">{{ user.email }}</div>
                  </div>



                  <div class="field">
                    <label>账户状态</label>
                    <div class="value">
                      <Tag value="已激活" severity="success" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <Divider />

            <div class="profile-actions">
              <Button
                label="修改密码"
                icon="pi pi-lock"
                class="p-button-outlined mr-2"
                @click="$router.push('/change-password')"
              />
              <Button label="登出" icon="pi pi-sign-out" severity="danger" @click="handleLogout" />
            </div>
          </div>

          <div v-else class="error-message">
            <Message severity="error">无法加载用户信息</Message>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.profile-container {
  padding: 2rem;
}

.card {
  background: var(--surface-card);
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

h1 {
  margin-top: 0;
  margin-bottom: 2rem;
  color: var(--text-color);
  font-size: 1.5rem;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
}

.loading-container p {
  margin-top: 1rem;
  color: var(--text-color-secondary);
}

.profile-avatar {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.profile-details .field {
  margin-bottom: 1.5rem;
}

.profile-details label {
  display: block;
  font-weight: 600;
  color: var(--text-color-secondary);
  margin-bottom: 0.5rem;
}

.profile-details .value {
  font-size: 1.1rem;
  color: var(--text-color);
}

.profile-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

@media (max-width: 768px) {
  .profile-container {
    padding: 1rem;
  }

  .card {
    padding: 1.5rem;
  }

  .profile-actions {
    flex-direction: column;
  }

  .profile-actions .p-button {
    margin-right: 0;
    margin-bottom: 0.5rem;
    width: 100%;
  }
}
</style>
