<script setup>
import { useRoute } from 'vue-router';
import { useToast } from 'primevue/usetoast';
import { onMounted } from 'vue';

const route = useRoute();
const toast = useToast();
const email = route.params.email || '您的邮箱';

onMounted(() => {
  toast.add({
    severity: 'info',
    summary: '魔法链接已发送',
    detail: `我们已向 ${email} 发送了一封包含登录链接的邮件`,
    life: 5000
  });
});
</script>

<template>
  <Toast position="top-right" />
  
  <div class="magic-link-sent-container">
    <Card class="magic-link-sent-card">
      <template #title>
        <h1 class="text-center">魔法链接已发送</h1>
      </template>
      
      <template #content>
        <div class="success-message">
          <i class="pi pi-envelope" style="font-size: 3rem; color: var(--primary-color);"></i>
          <h2>请检查您的邮箱</h2>
          <p>我们已向 <strong>{{ email }}</strong> 发送了一封包含登录链接的邮件。</p>
          <p>请检查您的邮箱，点击邮件中的链接即可登录。</p>
        </div>
        
        <Divider />
        
        <div class="note">
          <p>如果您没有收到邮件，请检查以下几点：</p>
          <ul>
            <li>确认邮箱地址是否正确</li>
            <li>检查垃圾邮件文件夹</li>
            <li>等待几分钟，邮件可能会有延迟</li>
          </ul>
          
          <div class="form-actions">
            <Button 
              label="返回登录页面" 
              icon="pi pi-arrow-left" 
              severity="secondary" 
              outlined
              @click="$router.push('/login')"
            />
          </div>
        </div>
      </template>
    </Card>
  </div>
</template>

<style scoped>
.magic-link-sent-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 2rem;
  background-color: var(--surface-ground);
}

.magic-link-sent-card {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.magic-link-sent-card :deep(.p-card-title) {
  padding-bottom: 0;
}

.magic-link-sent-card :deep(.p-card-content) {
  padding-top: 1rem;
}

.text-center {
  text-align: center;
}

h1 {
  font-size: 1.75rem;
  color: var(--primary-color);
  margin: 0;
  padding: 0;
}

.success-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem 0;
  text-align: center;
}

.success-message h2 {
  margin: 1rem 0;
  color: var(--text-color);
}

.success-message p {
  margin: 0.5rem 0;
  color: var(--text-color);
  line-height: 1.5;
}

.note {
  color: var(--text-color-secondary);
  font-size: 0.95rem;
  line-height: 1.5;
}

.note p {
  margin: 0.5rem 0;
}

.note ul {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.note li {
  margin-bottom: 0.5rem;
}

.form-actions {
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
}

@media (max-width: 576px) {
  .magic-link-sent-container {
    padding: 1rem;
  }
  
  .magic-link-sent-card {
    max-width: 100%;
  }
}
</style>
