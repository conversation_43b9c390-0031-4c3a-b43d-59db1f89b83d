import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
    },
    // 用户认证相关路由
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/auth/RegisterView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/auth/LoginView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/forgot-password',
      name: 'forgot-password',
      component: () => import('../views/auth/ForgotPasswordView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/reset-password/:token',
      name: 'reset-password',
      component: () => import('../views/auth/ResetPasswordView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/verify-email/:token',
      name: 'verify-email',
      component: () => import('../views/auth/VerifyEmailView.vue')
    },
    {
      path: '/magic-link-sent/:email',
      name: 'magic-link-sent',
      component: () => import('../views/auth/MagicLinkSentView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/magic-link/:token',
      name: 'magic-link-login',
      component: () => import('../views/auth/MagicLinkLoginView.vue'),
      meta: { requiresGuest: true }
    },
    // 用户相关路由
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/user/ProfileView.vue'),
      meta: { requiresAuth: true }
    },
    // 商品相关路由
    {
      path: '/product/:id',
      name: 'product-detail',
      component: () => import('../views/product/ProductDetailView.vue')
    },
  ],
})

// 全局前置守卫
router.beforeEach(async (to, _from, next) => {
  const authStore = useAuthStore()

  // 如果用户未登录但有token，尝试获取用户信息
  if (!authStore.user && authStore.token) {
    try {
      await authStore.fetchCurrentUser()
    } catch (error) {
      // 如果获取用户信息失败，清除token
      authStore.clearAuth()
    }
  }

  // 检查路由是否需要认证
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    // 如果需要认证但用户未登录，重定向到登录页
    next({ name: 'login' })
  }
  // 检查路由是否只允许未登录用户访问
  else if (to.meta.requiresGuest && authStore.isAuthenticated) {
    // 如果只允许未登录用户但用户已登录，重定向到首页
    next({ name: 'home' })
  }
  else {
    // 其他情况正常导航
    next()
  }
})

export default router
