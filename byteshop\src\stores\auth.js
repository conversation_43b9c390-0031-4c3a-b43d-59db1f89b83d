import { defineStore } from 'pinia';
import { authApi } from '@/services/api';

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    token: localStorage.getItem('token') || null,
    loading: false,
    error: null,
  }),

  getters: {
    isAuthenticated: (state) => !!state.token && !!state.user,
    isAdmin: (state) => state.user?.role === 'admin',
  },

  actions: {
    // 设置用户和token
    setAuth(user, token) {
      this.user = user;
      this.token = token;
      localStorage.setItem('token', token);
    },

    // 清除用户和token
    clearAuth() {
      this.user = null;
      this.token = null;
      localStorage.removeItem('token');
    },

    // 注册
    async register(userData) {
      this.loading = true;
      this.error = null;
      try {
        const response = await authApi.register(userData);
        // 注册成功后不自动登录，需要用户验证邮箱
        // 如果响应中有user和token，才设置登录状态
        if (response && response.user && response.token) {
          this.setAuth(response.user, response.token);
        }
        // 确保返回一个有效的对象
        return response || { success: true };
      } catch (error) {
        this.error = error.message;
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 登录
    async login(credentials) {
      this.loading = true;
      this.error = null;
      try {
        const response = await authApi.login(credentials);
        this.setAuth(response.user, response.token);
        return response;
      } catch (error) {
        this.error = error.message;
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 登出
    async logout() {
      this.clearAuth();
    },

    // 获取当前用户信息
    async fetchCurrentUser() {
      if (!this.token) return null;

      this.loading = true;
      this.error = null;
      try {
        const user = await authApi.getCurrentUser();
        this.user = user;
        return user;
      } catch (error) {
        this.error = error.message;
        this.clearAuth();
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 忘记密码
    async forgotPassword(email) {
      this.loading = true;
      this.error = null;
      try {
        const response = await authApi.forgotPassword(email);
        return response;
      } catch (error) {
        this.error = error.message;
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 重置密码
    async resetPassword(resetData) {
      this.loading = true;
      this.error = null;
      try {
        const response = await authApi.resetPassword(resetData);
        return response;
      } catch (error) {
        this.error = error.message;
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 验证邮箱
    async verifyEmail(token) {
      this.loading = true;
      this.error = null;
      try {
        const response = await authApi.verifyEmail(token);
        return response;
      } catch (error) {
        this.error = error.message;
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 魔法链接登录
    async requestMagicLink(email) {
      this.loading = true;
      this.error = null;
      try {
        const response = await authApi.magicLink(email);
        return response;
      } catch (error) {
        this.error = error.message;
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 使用魔法链接登录
    async loginWithMagicLink(token) {
      this.loading = true;
      this.error = null;
      try {
        const response = await authApi.loginWithMagicLink(token);
        console.log('Magic link login response:', response);

        // 处理不同的响应情况
        if (response && response.user && response.token) {
          // 情况1: 返回了user和token
          this.setAuth(response.user, response.token);
        } else if (response && response.token) {
          // 情况2: 只返回了token没有user
          this.token = response.token;
          localStorage.setItem('token', response.token);
          // 尝试获取当前用户信息
          await this.fetchCurrentUser();
        } else if (response && response.success) {
          // 情况3: 只返回了成功状态
          // 尝试获取当前用户信息
          await this.fetchCurrentUser();
        }
        return response || { success: true };
      } catch (error) {
        this.error = error.message;
        throw error;
      } finally {
        this.loading = false;
      }
    },
  },
});
