<script setup>
import { ref } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useToast } from 'primevue/usetoast';

const authStore = useAuthStore();
const toast = useToast();

const email = ref('');
const errors = ref({});
const successMessage = ref('');
const loading = ref(false);

const validateForm = () => {
  errors.value = {};
  let isValid = true;

  if (!email.value) {
    errors.value.email = '请输入电子邮箱';
    isValid = false;
  } else if (!/\S+@\S+\.\S+/.test(email.value)) {
    errors.value.email = '请输入有效的电子邮箱';
    isValid = false;
  }

  return isValid;
};

const handleSubmit = async () => {
  if (!validateForm()) return;

  loading.value = true;
  try {
    await authStore.forgotPassword(email.value);
    successMessage.value = '重置密码链接已发送到您的邮箱，请查收';
    
    toast.add({
      severity: 'success',
      summary: '邮件已发送',
      detail: '重置密码链接已发送到您的邮箱，请查收',
      life: 5000
    });
    
    email.value = '';
  } catch (error) {
    if (error.message) {
      errors.value.general = error.message;
      toast.add({
        severity: 'error',
        summary: '发送失败',
        detail: error.message,
        life: 5000
      });
    } else {
      errors.value.general = '发送重置密码链接失败，请稍后再试';
      toast.add({
        severity: 'error',
        summary: '发送失败',
        detail: '请稍后再试',
        life: 5000
      });
    }
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <Toast position="top-right" />
  
  <div class="forgot-password-container">
    <Card class="forgot-password-card">
      <template #title>
        <h1 class="text-center">忘记密码</h1>
      </template>
      
      <template #content>
        <div v-if="successMessage" class="success-message">
          <Message severity="success">{{ successMessage }}</Message>
        </div>
        
        <div v-if="errors.general" class="error-message">
          <Message severity="error">{{ errors.general }}</Message>
        </div>
        
        <p class="description">
          请输入您的电子邮箱，我们将向您发送重置密码的链接。
        </p>
        
        <form @submit.prevent="handleSubmit" class="forgot-form">
          <div class="field">
            <label for="email" class="form-label">电子邮箱</label>
            <span class="p-input-icon-left w-full">
              <i class="pi pi-envelope"></i>
              <InputText 
                id="email" 
                v-model="email" 
                type="email" 
                placeholder="请输入您的电子邮箱" 
                :class="{'p-invalid': errors.email}" 
                class="w-full" 
                required 
              />
            </span>
            <small v-if="errors.email" class="p-error">{{ errors.email }}</small>
          </div>
          
          <div class="form-actions">
            <Button 
              type="submit" 
              label="发送重置链接" 
              icon="pi pi-envelope" 
              :loading="loading" 
              :disabled="loading"
              severity="primary"
              raised
              class="w-full"
            />
          </div>
          
          <div class="form-footer">
            <router-link to="/login">返回登录</router-link>
          </div>
        </form>
      </template>
    </Card>
  </div>
</template>

<style scoped>
.forgot-password-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 2rem;
  background-color: var(--surface-ground);
}

.forgot-password-card {
  width: 100%;
  max-width: 450px;
  margin: 0 auto;
}

.forgot-password-card :deep(.p-card-title) {
  padding-bottom: 0;
}

.forgot-password-card :deep(.p-card-content) {
  padding-top: 1rem;
}

.forgot-form {
  width: 100%;
}

.text-center {
  text-align: center;
}

h1 {
  font-size: 1.75rem;
  color: var(--primary-color);
  margin: 0;
  padding: 0;
}

.description {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--text-color-secondary);
}

.field {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.w-full {
  width: 100%;
}

.success-message,
.error-message {
  margin-bottom: 1.5rem;
}

.p-error {
  color: var(--red-500);
  margin-top: 0.25rem;
  font-size: 0.875rem;
  display: block;
}

.form-actions {
  margin: 1.5rem 0;
}

.form-footer {
  text-align: center;
  margin-top: 1.5rem;
  color: var(--text-color-secondary);
  font-size: 0.875rem;
}

.form-footer a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.form-footer a:hover {
  text-decoration: underline;
}

@media (max-width: 576px) {
  .forgot-password-container {
    padding: 1rem;
  }
  
  .forgot-password-card {
    max-width: 100%;
  }
}
</style>
