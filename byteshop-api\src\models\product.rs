use async_trait::async_trait;
use sea_orm::entity::prelude::*;
use sea_orm::Set;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Deserialize, Serialize)]
#[sea_orm(table_name = "products")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub name: String,
    pub description: String,
    pub price: f64,
    pub image_urls: Json, // 存储多张图片URL的JSON数组
    pub is_active: bool,
    #[sea_orm(column_type = "Text")]
    pub details: String, // 详细介绍，可以是HTML或Markdown格式
    pub created_at: DateTimeUtc,
    pub updated_at: DateTimeUtc,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

#[async_trait::async_trait]
impl ActiveModelBehavior for ActiveModel {
    // 在保存前自动更新updated_at字段
    async fn before_save<C>(mut self, _db: &C, insert: bool) -> Result<Self, DbErr>
    where
        C: ConnectionTrait,
    {
        self.updated_at = Set(chrono::Utc::now());
        if insert {
            self.created_at = Set(chrono::Utc::now());
        }
        Ok(self)
    }
}

// 用于创建新商品的DTO
#[derive(Debug, Deserialize, Serialize)]
pub struct CreateProduct {
    pub name: String,
    pub description: String,
    pub price: f64,
    pub image_urls: Vec<String>,
    pub details: String,
    pub is_active: Option<bool>,
}

// 用于更新商品的DTO
#[derive(Debug, Deserialize, Serialize)]
pub struct UpdateProduct {
    pub name: Option<String>,
    pub description: Option<String>,
    pub price: Option<f64>,
    pub image_urls: Option<Vec<String>>,
    pub details: Option<String>,
    pub is_active: Option<bool>,
}

// 用于返回商品列表的DTO
#[derive(Debug, Deserialize, Serialize)]
pub struct ProductListResponse {
    pub id: i32,
    pub name: String,
    pub description: String,
    pub price: f64,
    pub image_urls: Vec<String>,
    pub is_active: bool,
}

// 用于返回商品详情的DTO
#[derive(Debug, Deserialize, Serialize)]
pub struct ProductDetailResponse {
    pub id: i32,
    pub name: String,
    pub description: String,
    pub price: f64,
    pub image_urls: Vec<String>,
    pub details: String,
    pub is_active: bool,
    pub created_at: String,
    pub updated_at: String,
}
