# Loco configuration file documentation

# Application logging configuration
logger:
  # Enable or disable logging.
  enable: false
  # Enable pretty backtrace (sets RUST_BACKTRACE=1)
  pretty_backtrace: true
  # Log level, options: trace, debug, info, warn or error.
  level: debug
  # Define the logging format. options: compact, pretty or json
  format: compact
  # By default the logger has filtering only logs that came from your code or logs that came from `loco` framework. to see all third party libraries
  # Uncomment the line below to override to see all third party libraries you can enable this config and override the logger filters.
  # override_filter: trace

# Web server configuration
server:
  # Port on which the server will listen. the server binding is 0.0.0.0:{PORT}
  port: 5150
  # The UI hostname or IP address that mailers will point to.
  host: http://localhost
  # Out of the box middleware configuration. to disable middleware you can changed the `enable` field to `false` of comment the middleware block
  middlewares:

# Worker Configuration
workers:
  # specifies the worker mode. Options:
  #   - BackgroundQueue - Workers operate asynchronously in the background, processing queued.
  #   - ForegroundBlocking - Workers operate in the foreground and block until tasks are completed.
  #   - BackgroundAsync - Workers operate asynchronously in the background, processing tasks with async capabilities.
  mode: ForegroundBlocking

  

# Mailer Configuration.
mailer:
  stub: true
  # SMTP mailer configuration.
  smtp:
    # Enable/Disable smtp mailer.
    enable: true
    # SMTP server host. e.x localhost, smtp.gmail.com
    host: localhost
    # SMTP server port
    port: 1025
    # Use secure connection (SSL/TLS).
    secure: false
    # auth:
    #   user:
    #   password:

# Initializers Configuration
# initializers:
#  oauth2:
#    authorization_code: # Authorization code grant type
#      - client_identifier: google # Identifier for the OAuth2 provider. Replace 'google' with your provider's name if different, must be unique within the oauth2 config.
#        ... other fields

# Database Configuration
database:
  # Database connection URI
  uri: {{ get_env(name="DATABASE_URL", default="sqlite://byteshop-api_test.sqlite?mode=rwc") }}
  # When enabled, the sql query will be logged.
  enable_logging: false
  # Set the timeout duration when acquiring a connection.
  connect_timeout: {{ get_env(name="DB_CONNECT_TIMEOUT", default="500") }}
  # Set the idle duration before closing a connection.
  idle_timeout: {{ get_env(name="DB_IDLE_TIMEOUT", default="500") }}
  # Minimum number of connections for a pool.
  min_connections: {{ get_env(name="DB_MIN_CONNECTIONS", default="1") }}
  # Maximum number of connections for a pool.
  max_connections: {{ get_env(name="DB_MAX_CONNECTIONS", default="1") }}
  # Run migration up when application loaded
  auto_migrate: true
  # Truncate database when application loaded. This is a dangerous operation, make sure that you using this flag only on dev environments or test mode
  dangerously_truncate: true
  # Recreating schema when application loaded.  This is a dangerous operation, make sure that you using this flag only on dev environments or test mode
  dangerously_recreate: true

# Authentication Configuration
auth:
  # JWT authentication
  jwt:
    # Secret key for token generation and verification
    secret: dVoNfXMzv1g4ap8KVGju
    # Token expiration time in seconds
    expiration: 604800 # 7 days
