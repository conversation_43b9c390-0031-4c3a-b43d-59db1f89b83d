<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { useToast } from 'primevue/usetoast';

const router = useRouter();
const authStore = useAuthStore();
const toast = useToast();

const form = ref({
  email: '',
  password: '',
});

const errors = ref({});
const loading = ref(false);

const validateForm = () => {
  errors.value = {};
  let isValid = true;

  if (!form.value.email) {
    errors.value.email = '请输入电子邮箱';
    isValid = false;
  }

  if (!form.value.password) {
    errors.value.password = '请输入密码';
    isValid = false;
  }

  return isValid;
};

const handleSubmit = async () => {
  if (!validateForm()) return;

  loading.value = true;
  try {
    await authStore.login({
      email: form.value.email,
      password: form.value.password,
    });

    toast.add({
      severity: 'success',
      summary: '登录成功',
      detail: '欢迎回来！',
      life: 3000
    });

    // 登录成功后跳转到首页
    router.push('/');
  } catch (error) {
    if (error.message) {
      errors.value.general = error.message;
      toast.add({
        severity: 'error',
        summary: '登录失败',
        detail: error.message,
        life: 5000
      });
    } else {
      errors.value.general = '登录失败，请检查您的邮箱和密码';
      toast.add({
        severity: 'error',
        summary: '登录失败',
        detail: '请检查您的邮箱和密码',
        life: 5000
      });
    }
  } finally {
    loading.value = false;
  }
};

const handleMagicLink = async () => {
  if (!form.value.email) {
    errors.value.email = '请输入电子邮箱';
    toast.add({
      severity: 'warn',
      summary: '无法发送魔法链接',
      detail: '请输入您的电子邮箱',
      life: 3000
    });
    return;
  }

  loading.value = true;
  try {
    await authStore.requestMagicLink(form.value.email);
    toast.add({
      severity: 'info',
      summary: '魔法链接已发送',
      detail: '请检查您的邮箱',
      life: 3000
    });
    router.push({
      name: 'magic-link-sent',
      params: { email: form.value.email },
    });
  } catch (error) {
    if (error.message) {
      errors.value.general = error.message;
      toast.add({
        severity: 'error',
        summary: '发送失败',
        detail: error.message,
        life: 5000
      });
    } else {
      errors.value.general = '发送魔法链接失败，请稍后再试';
      toast.add({
        severity: 'error',
        summary: '发送失败',
        detail: '请稍后再试',
        life: 5000
      });
    }
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <Toast position="top-right" />

  <div class="login-container">
    <Card class="login-card">
      <template #title>
        <h1 class="text-center">登录</h1>
      </template>

      <template #content>
        <div v-if="errors.general" class="error-message">
          <Message severity="error">{{ errors.general }}</Message>
        </div>

        <form @submit.prevent="handleSubmit" class="login-form">
          <div class="field">
            <label for="email" class="form-label">电子邮箱</label>
            <span class="p-input-icon-left w-full">
              <i class="pi pi-envelope"></i>
              <InputText
                id="email"
                v-model="form.email"
                type="email"
                placeholder="请输入您的电子邮箱"
                :class="{'p-invalid': errors.email}"
                class="w-full"
                required
              />
            </span>
            <small v-if="errors.email" class="p-error">{{ errors.email }}</small>
          </div>

          <div class="field">
            <label for="password" class="form-label">密码</label>
            <span class="p-input-icon-left w-full">
              <i class="pi pi-lock"></i>
              <Password
                id="password"
                v-model="form.password"
                placeholder="请输入密码"
                :class="{'p-invalid': errors.password}"
                :feedback="false"
                toggleMask
                inputClass="w-full"
                class="w-full"
                required
              />
            </span>
            <small v-if="errors.password" class="p-error">{{ errors.password }}</small>
          </div>

          <div class="forgot-password">
            <router-link to="/forgot-password">忘记密码？</router-link>
          </div>

          <div class="form-actions">
            <Button
              type="submit"
              label="登录"
              icon="pi pi-sign-in"
              :loading="loading"
              :disabled="loading"
              severity="primary"
              raised
              class="w-full"
            />
          </div>

          <Divider align="center">
            <span class="text-divider">或</span>
          </Divider>

          <div class="form-actions">
            <Button
              type="button"
              label="使用魔法链接登录"
              icon="pi pi-envelope"
              :loading="loading"
              :disabled="loading"
              severity="secondary"
              outlined
              class="w-full"
              @click="handleMagicLink"
            />
          </div>

          <div class="form-footer">
            还没有账户？
            <router-link to="/register">注册</router-link>
          </div>
        </form>
      </template>
    </Card>
  </div>
</template>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 2rem;
}

.login-card {
  width: 100%;
  max-width: 450px;
  margin: 0 auto;
}

.login-card :deep(.p-card-title) {
  padding-bottom: 0;
}

.login-card :deep(.p-card-content) {
  padding-top: 1rem;
}

.login-form {
  width: 100%;
}

.field {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.w-full {
  width: 100%;
}

:deep(.p-password) {
  width: 100%;
}

:deep(.p-password-input) {
  width: 100%;
}

h1 {
  font-size: 1.75rem;
  color: var(--primary-color);
  margin: 0;
  padding: 0;
}

.text-center {
  text-align: center;
}

.error-message {
  margin-bottom: 1.5rem;
}

.p-error {
  color: var(--red-500);
  margin-top: 0.25rem;
  font-size: 0.875rem;
  display: block;
}

.forgot-password {
  text-align: right;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
}

.forgot-password a {
  color: var(--primary-color);
  text-decoration: none;
}

.forgot-password a:hover {
  text-decoration: underline;
}

.form-actions {
  margin: 1.5rem 0;
}

.text-divider {
  color: var(--text-color-secondary);
  font-size: 0.875rem;
  background-color: var(--surface-card);
  padding: 0 0.5rem;
}

.form-footer {
  text-align: center;
  margin-top: 1.5rem;
  color: var(--text-color-secondary);
  font-size: 0.875rem;
}

.form-footer a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.form-footer a:hover {
  text-decoration: underline;
}

@media (max-width: 576px) {
  .login-container {
    padding: 1rem;
  }

  .login-card {
    max-width: 100%;
  }
}
</style>
