<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useToast } from 'primevue/usetoast';
import api from '@/services/api';

const router = useRouter();
const toast = useToast();

const form = ref({
  name: '',
  description: '',
  price: null,
  stock: null,
  image_url: ''
});

const errors = ref({});
const loading = ref(false);

const validateForm = () => {
  errors.value = {};
  let isValid = true;

  if (!form.value.name) {
    errors.value.name = '请输入商品名称';
    isValid = false;
  }

  if (!form.value.description) {
    errors.value.description = '请输入商品描述';
    isValid = false;
  }

  if (form.value.price === null || form.value.price === '') {
    errors.value.price = '请输入商品价格';
    isValid = false;
  } else if (isNaN(form.value.price) || form.value.price <= 0) {
    errors.value.price = '价格必须是大于0的数字';
    isValid = false;
  }

  if (form.value.stock === null || form.value.stock === '') {
    errors.value.stock = '请输入商品库存';
    isValid = false;
  } else if (isNaN(form.value.stock) || form.value.stock < 0 || !Number.isInteger(Number(form.value.stock))) {
    errors.value.stock = '库存必须是非负整数';
    isValid = false;
  }

  return isValid;
};

const handleSubmit = async () => {
  if (!validateForm()) return;

  loading.value = true;
  try {
    const productData = {
      ...form.value,
      price: Number(form.value.price),
      stock: Number(form.value.stock)
    };

    const response = await api.products.createProduct(productData);
    
    toast.add({
      severity: 'success',
      summary: '创建成功',
      detail: '商品已成功创建',
      life: 3000
    });
    
    router.push(`/products/${response.id}`);
  } catch (error) {
    errors.value.general = error.message || '创建商品失败';
    
    toast.add({
      severity: 'error',
      summary: '创建失败',
      detail: errors.value.general,
      life: 5000
    });
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <div class="product-create-container">
    <div class="card">
      <div class="header">
        <Button 
          icon="pi pi-arrow-left" 
          text 
          @click="router.push('/products')" 
          class="back-button"
        />
        <h1>创建商品</h1>
      </div>
      
      <div v-if="errors.general" class="error-message">
        <Message severity="error">{{ errors.general }}</Message>
      </div>
      
      <form @submit.prevent="handleSubmit" class="product-form">
        <div class="grid">
          <div class="col-12">
            <div class="field">
              <label for="name" class="form-label">商品名称 <span class="required">*</span></label>
              <InputText 
                id="name" 
                v-model="form.name" 
                placeholder="请输入商品名称" 
                :class="{'p-invalid': errors.name}" 
                class="w-full" 
                required 
              />
              <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
            </div>
          </div>
          
          <div class="col-12 md:col-6">
            <div class="field">
              <label for="price" class="form-label">价格 <span class="required">*</span></label>
              <InputNumber 
                id="price" 
                v-model="form.price" 
                placeholder="请输入商品价格" 
                :class="{'p-invalid': errors.price}" 
                mode="currency" 
                currency="CNY" 
                locale="zh-CN"
                class="w-full" 
                required 
              />
              <small v-if="errors.price" class="p-error">{{ errors.price }}</small>
            </div>
          </div>
          
          <div class="col-12 md:col-6">
            <div class="field">
              <label for="stock" class="form-label">库存 <span class="required">*</span></label>
              <InputNumber 
                id="stock" 
                v-model="form.stock" 
                placeholder="请输入商品库存" 
                :class="{'p-invalid': errors.stock}" 
                class="w-full" 
                required 
                :min="0"
                :useGrouping="false"
              />
              <small v-if="errors.stock" class="p-error">{{ errors.stock }}</small>
            </div>
          </div>
          
          <div class="col-12">
            <div class="field">
              <label for="image_url" class="form-label">图片URL</label>
              <InputText 
                id="image_url" 
                v-model="form.image_url" 
                placeholder="请输入商品图片URL" 
                class="w-full" 
              />
              <small class="helper-text">输入商品图片的URL地址</small>
            </div>
          </div>
          
          <div class="col-12">
            <div class="field">
              <label for="description" class="form-label">商品描述 <span class="required">*</span></label>
              <Textarea 
                id="description" 
                v-model="form.description" 
                placeholder="请输入商品描述" 
                :class="{'p-invalid': errors.description}" 
                class="w-full" 
                rows="5" 
                required 
              />
              <small v-if="errors.description" class="p-error">{{ errors.description }}</small>
            </div>
          </div>
        </div>
        
        <div class="form-actions">
          <Button 
            type="button" 
            label="取消" 
            icon="pi pi-times" 
            severity="secondary" 
            outlined
            @click="router.push('/products')" 
            class="mr-2"
            :disabled="loading"
          />
          <Button 
            type="submit" 
            label="创建商品" 
            icon="pi pi-check" 
            severity="success" 
            :loading="loading" 
            :disabled="loading"
          />
        </div>
      </form>
    </div>
  </div>
</template>

<style scoped>
.product-create-container {
  padding: 2rem;
}

.card {
  background: var(--surface-card);
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
}

.back-button {
  margin-right: 1rem;
}

h1 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--text-color);
}

.error-message {
  margin-bottom: 1.5rem;
}

.product-form {
  width: 100%;
}

.field {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.required {
  color: var(--red-500);
}

.w-full {
  width: 100%;
}

.p-error {
  color: var(--red-500);
  margin-top: 0.25rem;
  font-size: 0.875rem;
  display: block;
}

.helper-text {
  color: var(--text-color-secondary);
  margin-top: 0.25rem;
  font-size: 0.875rem;
  display: block;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 2rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

@media (max-width: 768px) {
  .product-create-container {
    padding: 1rem;
  }
  
  .form-actions {
    flex-direction: column-reverse;
  }
  
  .form-actions .p-button {
    margin-right: 0;
    margin-bottom: 0.5rem;
    width: 100%;
  }
}
</style>
