<script setup>
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { useToast } from 'primevue/usetoast';

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const toast = useToast();

const form = ref({
  password: '',
  passwordConfirmation: '',
});

const errors = ref({});
const successMessage = ref('');
const loading = ref(false);

// 从URL中获取token
const token = route.params.token;

const validateForm = () => {
  errors.value = {};
  let isValid = true;

  if (!form.value.password) {
    errors.value.password = '请输入新密码';
    isValid = false;
  } else if (form.value.password.length < 8) {
    errors.value.password = '密码长度至少为8个字符';
    isValid = false;
  }

  if (form.value.password !== form.value.passwordConfirmation) {
    errors.value.passwordConfirmation = '两次输入的密码不一致';
    isValid = false;
  }

  return isValid;
};

const handleSubmit = async () => {
  if (!validateForm()) return;

  loading.value = true;
  try {
    await authStore.resetPassword({
      token,
      password: form.value.password,
      password_confirmation: form.value.passwordConfirmation,
    });
    
    successMessage.value = '密码重置成功！';
    
    toast.add({
      severity: 'success',
      summary: '密码重置成功',
      detail: '您的密码已成功重置，请使用新密码登录',
      life: 5000
    });
    
    // 重置成功后跳转到登录页
    setTimeout(() => {
      router.push('/login');
    }, 2000);
  } catch (error) {
    if (error.message) {
      errors.value.general = error.message;
      toast.add({
        severity: 'error',
        summary: '重置失败',
        detail: error.message,
        life: 5000
      });
    } else {
      errors.value.general = '重置密码失败，请稍后再试';
      toast.add({
        severity: 'error',
        summary: '重置失败',
        detail: '请稍后再试',
        life: 5000
      });
    }
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <Toast position="top-right" />
  
  <div class="reset-password-container">
    <Card class="reset-password-card">
      <template #title>
        <h1 class="text-center">重置密码</h1>
      </template>
      
      <template #content>
        <div v-if="successMessage" class="success-message">
          <Message severity="success">{{ successMessage }}</Message>
        </div>
        
        <div v-else>
          <div v-if="errors.general" class="error-message">
            <Message severity="error">{{ errors.general }}</Message>
          </div>
          
          <form @submit.prevent="handleSubmit" class="reset-form">
            <div class="field">
              <label for="password" class="form-label">新密码</label>
              <span class="p-input-icon-left w-full">
                <i class="pi pi-lock"></i>
                <Password 
                  id="password" 
                  v-model="form.password" 
                  placeholder="请输入新密码" 
                  :class="{'p-invalid': errors.password}" 
                  :feedback="true"
                  toggleMask
                  inputClass="w-full" 
                  class="w-full" 
                  required 
                />
              </span>
              <small v-if="errors.password" class="p-error">{{ errors.password }}</small>
            </div>
            
            <div class="field">
              <label for="passwordConfirmation" class="form-label">确认新密码</label>
              <span class="p-input-icon-left w-full">
                <i class="pi pi-lock-open"></i>
                <Password 
                  id="passwordConfirmation" 
                  v-model="form.passwordConfirmation" 
                  placeholder="请再次输入新密码" 
                  :class="{'p-invalid': errors.passwordConfirmation}" 
                  :feedback="false"
                  toggleMask
                  inputClass="w-full" 
                  class="w-full" 
                  required 
                />
              </span>
              <small v-if="errors.passwordConfirmation" class="p-error">{{ errors.passwordConfirmation }}</small>
            </div>
            
            <div class="form-actions">
              <Button 
                type="submit" 
                label="重置密码" 
                icon="pi pi-refresh" 
                :loading="loading" 
                :disabled="loading"
                severity="primary"
                raised
                class="w-full"
              />
            </div>
            
            <div class="form-footer">
              <router-link to="/login">返回登录</router-link>
            </div>
          </form>
        </div>
      </template>
    </Card>
  </div>
</template>

<style scoped>
.reset-password-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 2rem;
  background-color: var(--surface-ground);
}

.reset-password-card {
  width: 100%;
  max-width: 450px;
  margin: 0 auto;
}

.reset-password-card :deep(.p-card-title) {
  padding-bottom: 0;
}

.reset-password-card :deep(.p-card-content) {
  padding-top: 1rem;
}

.reset-form {
  width: 100%;
}

.text-center {
  text-align: center;
}

h1 {
  font-size: 1.75rem;
  color: var(--primary-color);
  margin: 0;
  padding: 0;
}

.field {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.w-full {
  width: 100%;
}

:deep(.p-password) {
  width: 100%;
}

:deep(.p-password-input) {
  width: 100%;
}

.success-message,
.error-message {
  margin-bottom: 1.5rem;
}

.p-error {
  color: var(--red-500);
  margin-top: 0.25rem;
  font-size: 0.875rem;
  display: block;
}

.form-actions {
  margin: 1.5rem 0;
}

.form-footer {
  text-align: center;
  margin-top: 1.5rem;
  color: var(--text-color-secondary);
  font-size: 0.875rem;
}

.form-footer a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.form-footer a:hover {
  text-decoration: underline;
}

@media (max-width: 576px) {
  .reset-password-container {
    padding: 1rem;
  }
  
  .reset-password-card {
    max-width: 100%;
  }
}
</style>
