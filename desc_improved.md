# ByteShop 网上商城项目需求文档

## 目录
1. [项目概述](#项目概述)
2. [商城功能](#商城功能)
3. [许可证验证服务](#许可证验证服务)
4. [许可证要求](#许可证要求)
5. [注册码要求](#注册码要求)
6. [商品管理](#商品管理)
7. [项目架构](#项目架构)
   - [项目一：商城前台页面](#项目一商城前台页面前端项目)
   - [项目二：后台管理页面](#项目二后台管理页面前端项目)
   - [项目三：商城API接口](#项目三商城api接口后端项目)
8. [数据管理](#数据管理)
9. [安全要求](#安全要求)
10. [部署与运维](#部署与运维)
11. [项目管理](#项目管理)

## 项目概述

ByteShop是一个专注于销售软件许可的网上商城，为用户提供便捷的软件授权购买体验。本项目旨在建立一个完整的软件许可销售和验证系统，包括前台商城、后台管理和API接口服务。

## 商城功能

1. 软件产品展示，包括多张图片、文字介绍和详细规格
2. 用户注册登录等基本功能
3. 用户购买软件产品的许可（不需要购物车功能）
4. 用户查看订单、许可证状态等信息
5. 用户账户管理（修改个人信息、密码重置等）
6. 订单状态跟踪和通知系统

## 许可证验证服务

1. 提供软件许可证验证功能的接口
2. 支持许可证状态查询（有效、过期、已吊销等）
3. 提供许可证绑定和解绑服务
4. 许可证使用情况监控和异常检测

## 许可证要求

1. 为了方便用户购买，购买时生成注册码，而不是直接生成许可证，因为生成许可证需要客户的机器码等信息
2. 用户在软件产品上输入注册码，然后通过网络验证注册码，验证成功就根据机器码生成许可证，并且可以解绑，解绑之后可以在别的机器上使用
3. 绑定机器和解绑机器时需要联网验证
4. 许可证有过期时间，系统应提供到期提醒功能
5. 为了方便用户购买，购买时不需要知道机器码等信息，只需要知道用户购买了多长时间的许可，并发送给用户一个注册码
6. 用户收到注册码后，可以在软件上输入注册码激活，这时联网验证注册码（可以一并发送机器码），验证成功生成许可证，此时软件授权完成，许可证保留在用户计算机上，可以不需要网络就能使用
7. 为了防止用户作弊，比如修改计算机机器码，使用许可证时，如果网络可用，会发送许可证使用信息到服务器，如果一个许可证被滥用，可以吊销许可证
8. 许可证信息需要使用非对称加密，在服务端加密生成许可证
9. 许可证格式应包含：产品ID、用户ID、机器码、过期时间、许可级别、数字签名等信息
10. 系统应记录许可证的所有操作历史，包括创建、绑定、解绑、验证和吊销等

## 注册码要求

1. 每种软件产品注册码都分为日卡、周卡、半月卡、月卡、季卡、半年卡、年卡
2. 销售出的注册码需要唯一，并且不可猜测
3. 注册码应具有一定的格式规范，便于用户输入和识别
4. 注册码应包含产品信息和有效期信息的编码
5. 系统应支持批量生成注册码功能
6. 注册码应有状态标识：未使用、已使用、已过期、已作废等

## 商品管理

1. 网站展示不同的软件商品
2. 有详细的软件介绍、功能列表、系统要求和用户评价
3. 用户选择软件购买，然后选择购买时长（日卡、周卡、半月卡、月卡、季卡、半年卡、年卡），付款成功后收到注册码
4. 支持商品分类、搜索和筛选功能
5. 商品可设置促销活动和折扣信息

## 项目架构

项目拆分为以下几个部分：

### 项目一：商城前台页面（前端项目）

1. 展示商品，商品的种类不会太多，大概只有几种
2. 用户注册、登录，只能用电子邮件注册登录
3. 购买商品，选择要购买的商品、数量，未登录的用户也可购买，需要填写电子邮件地址，即可提交订单，提交订单时需要邮箱收到的验证码，防止客户填错邮件地址
4. 不填写电子邮箱也可购买，付款成功之后直接跳转页面，把注册码展示给客户，并提供复制到剪切板、下载注册码按钮，并提醒用户保管好注册码
5. 付款使用支付宝、微信支付等主流支付方式
6. 软件注册码是付款成功之后自动生成的
7. 使用Node.js、Vue.js，并且不使用TypeScript，UI组件用PrimeVue，并且要适配移动端和PC端
8. 实现响应式设计，确保在不同设备上有良好的用户体验
9. 提供用户订单历史和注册码管理界面
10. 集成邮件通知系统，在订单状态变更时通知用户

### 项目二：后台管理页面（前端项目）

1. 后台管理页面不对外开放，需要登录才能进入
2. 可以管理商品（添加、编辑、下架）
3. 可以查看订单和订单统计数据
4. 可以管理注册码，比如批量生成一批注册码并下载下来
5. 可以手动管理注册码，比如禁用
6. 可以管理许可证（查看、吊销、延期等）
7. 提供数据分析和报表功能，展示销售趋势和用户行为
8. 支持管理员账户管理和权限控制
9. 使用Node.js、Vue.js，并且不使用TypeScript，UI组件用PrimeVue，只需要适配PC端
10. 提供系统日志查看功能

### 项目三：商城API接口（后端项目）

1. 提供商城前台和后台管理所需要的API
2. 使用Rust的Web开发框架Loco开发，生产环境数据库使用PostgreSQL，本地开发环境数据库使用SQLite
3. 实现接口安全校验和权限控制
4. 提供完整的API文档，使用Swagger或类似工具
5. 实现数据验证和错误处理机制
6. 支持异步任务处理（如邮件发送、注册码生成等）
7. 实现日志记录和监控功能
8. 提供数据备份和恢复接口
9. 支持水平扩展，以应对高并发场景
10. 实现限流和防DDoS攻击措施

## 数据管理

1. 数据库设计应遵循规范化原则，确保数据完整性和一致性
2. 提供定期自动备份功能，支持增量备份
3. 支持数据导出和导入功能，便于系统迁移
4. 实现数据库版本控制和迁移脚本
5. 敏感数据（如用户密码）必须加密存储
6. 提供数据清理和归档策略，处理历史数据

## 安全要求

1. 实现完善的用户认证和授权机制
2. API接口需要实现访问控制和防篡改措施
3. 防止常见的Web安全漏洞（XSS、CSRF、SQL注入等）
4. 实现API请求限流和防爬虫措施
5. 敏感操作需要二次验证
6. 实现安全日志记录，便于审计和追踪
7. 许可证验证系统需要防止重放攻击和伪造请求

## 部署与运维

1. 提供完整的部署文档和脚本
2. 支持Docker容器化部署
3. 实现健康检查和自动恢复机制
4. 提供性能监控和告警系统
5. 支持配置热更新，无需重启服务
6. 实现日志集中管理和分析
7. 提供系统扩容和缩容方案

## 项目管理

1. 使用Git进行版本控制，遵循GitFlow或类似的分支管理策略
2. 实现自动化测试，包括单元测试、集成测试和端到端测试
3. 设置CI/CD流程，实现自动构建和部署
4. 制定明确的开发规范和代码审查流程
5. 使用项目管理工具跟踪任务和进度
6. 建立定期的项目评审和迭代计划

## 其他要求

1. 可以方便地备份数据，比如迁移到其他地方，要方便把数据迁移出来
2. 在当前目录下(byteshop)创建3个项目（商城前台页面、后台管理页面、API接口）
3. 系统应具有良好的可扩展性，便于未来添加新功能
4. 提供详细的用户手册和开发文档
5. 考虑未来可能的国际化需求，预留多语言支持接口
